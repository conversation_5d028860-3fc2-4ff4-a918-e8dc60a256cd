import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';
import * as _ from 'lodash';

import { QueryOutputVo } from '@api/query-engine/model';
import { getDateFields, getNumberFields } from '@common/chart';
import { HighChartBuilder, SeriesItem } from '@common/chart/highcharts';
import { _pad, createValueElement, getWeekByDate, groupBy, toDecimals, toNumber } from '@common/function';
import { DtType } from '@common/service/query-engine';
import { GraphComponent } from '@shared/components/graph';

const dtTypeMap = new Map([
  ['dt', '日'],
  ['yw', '周'],
  ['ym', '月'],
])

function tooltipFormatter({ sortFn, series, dtType }: MetricTrendArea) {
  return function(param: any) {
    const result = [];
    const map = new Map();
    const params = this.points.sort(sortFn);
    
    params.forEach(item => {
      if (map.has(item.series.name)) {
        const arr = map.get(item.series.name) as any[];
        arr.push(item);
        arr.reverse();
      } else {
        map.set(item.series.name, [item]);
      }
    });
    
    const merged = [...map.values()].flat(1) as Array<{
      x: string,
      y: number,
      series: { 
        name: string,
        data: Array<{ y: number }>
      },
      point: {
        index: number,
        yw: { compare: number, diff: number, ratio: number },
        ym: { compare: number, diff: number, ratio: number }
      }
    }>;
    const seriesName = merged.at(0).series.name;

    const thisYear = new Date().getFullYear();
    const lastYear = thisYear - 1;
    const beforeLastYear = lastYear - 1;
    
    result.push('<table class="shadow-xl text-xs table-auto text-center bg-neutral-300 border-separate! border-spacing-px">');

    result.push('<thead>');
    result.push(`<tr>`);
    result.push(`<th class="py-1 px-5 bg-white" rowspan="2">指标</th>`);
    merged.forEach((params) => {
      const { series: { name }, x } = params;
      const [year] = name.split('-').map(toNumber);
      const [, day] = x?.split('-')?.map(toNumber);
      const month = (<string>x).match(/\d+/g);
      const isThisYear = year === thisYear;
      const label = isThisYear ? `当前${dtTypeMap.get(dtType)}:` : '';
      const dateLabel = getWeekByDate(`${year}-${x}`, dtType);
      const weekLabel = `(${getWeekByDate(`${year}-${_pad(+month[0])}`, dtType)})`.replace(/\s/g, '');
      const dateTitle = (
        dtType === 'dt' 
          ? `${year}.${x.replace(/-/g, '.')} ${dateLabel}` // 例：2024.04.14 周日
          : dtType === 'yw'
            ? `${year}年${x} ${weekLabel}` // 例：2024年第18周(04-29~05-05)
              : `${year}.${_pad(+month[0])}`
      );
      
      // console.log({ name, month: month[0], x });
      
      result.push(`
        <th class="py-1 px-5 bg-white" colspan="1">
          ${label}
          ${dateTitle}
        </th>
      `);
    })
    result.push(`</tr>`);

    result.push(`<tr>`);
    merged.forEach((params) => {
      const { series: { name: seriesName } } = params;
      const [year] = seriesName.split('-').map(toNumber);
      const isThisYear = year === thisYear;
      const isLastYear = year === lastYear;
      const isBeforeLastYear = year === beforeLastYear;
      
      let radioLabel = '';

      if (isThisYear) {
        radioLabel = (
          dtType === 'dt' ? '周同比': 
          dtType === 'yw' ? '周环比':
          dtType === 'ym' ? '月环比' : ''
        );
      }
      
      if (isLastYear) {
        radioLabel = `当${dtTypeMap.get(dtType)}同比${lastYear}年`
      }

      if (isBeforeLastYear) {
        radioLabel = `当${dtTypeMap.get(dtType)}同比${beforeLastYear}年`
      }

      result.push(`<th class="py-1 px-5 min-w-30 bg-white">数值</th>`);
      // result.push(`<th class="py-1 px-5 min-w-30 bg-white">${radioLabel}</th>`);
    })
    result.push(`</tr>`);
    result.push('</thead>');

    result.push(`<tr>`);
    result.push(`<td class="py-1 px-5 bg-white">${seriesName.replace(/(^\d+\-)/, '')}</td>`)
    
    // console.log('[merged]', merged);
    
    const thisYearSeries = merged.find(item => item.series.name.includes(`${thisYear}`));
    const lastYearSeries = merged.find(item => item.series.name.includes(`${lastYear}`));
    const beforeLastYearSeries = merged.find(item => item.series.name.includes(`${beforeLastYear}`));

    merged.forEach((params) => {
      const { series: { name: seriesName }, x, y: value, point: { yw, ym, index } } = params;
      const { isPercent } = series.find(item => item.name === seriesName);
      const unit = isPercent ? 'pp' : '%';

      const [year] = seriesName.split('-').map(toNumber);
      const isThisYear = year === thisYear;
      const isLastYear = year === lastYear;
      const isBeforeLastYear = year === beforeLastYear;

      const thisYearValue = thisYearSeries?.series?.data?.at(index)?.y;
      const lastYearValue = lastYearSeries?.series?.data?.at(index)?.y;
      const beforeLastYearValue = beforeLastYearSeries?.series?.data?.at(index)?.y;

      // console.log({ name: seriesName, index, isThisYear, isLastYear, isBeforeLastYear });
      // console.log(params);
      // console.log({ yw, ym, seriesName, });
      
      let radioTemplate = '';
      // 当前年 - 去年 ((当前年 - 去年) / 去年)
      // 当前年 - 前年 ((当前年 - 前年) / 前年)

      if (isThisYear) {
        const { diff, ratio } = dtType === 'ym' ? ym : yw;

        radioTemplate = `
          ${createValueElement(diff, '{n}')} 
          ${createValueElement(ratio, `({n}${unit})`)}
        `;
      }

      if (isLastYear) {
        const diff = thisYearValue - lastYearValue;
        const ratio = diff / lastYearValue;

        radioTemplate = !thisYearSeries ? '--' : `
          ${createValueElement(diff, '{n}')} 
          ${createValueElement(ratio * 100, `({n}${unit})`)}
        `;
      }

      if (isBeforeLastYear) {
        const diff = thisYearValue - beforeLastYearValue;
        const ratio = diff / beforeLastYearValue;

        radioTemplate = !thisYearSeries ? '--' : `
          ${createValueElement(diff, '{n}')} 
          ${createValueElement(ratio * 100, `({n}${unit})`)}
        `;
      }

      const valueTemplate = `${
        Number.isFinite(value) 
          ? isPercent 
            ? (toDecimals(value) + '%')
            : Intl.NumberFormat().format(value)
          : '-'
      }`;

      // debugger
      result.push(`<td class="py-1 px-5 bg-white text-xs">${ valueTemplate }</td>`);
      // result.push(`<td class="py-1 px-5 bg-white">${ radioTemplate }</td>`);
    })

    result.push('</tr>');
    result.push('</table>');

    return result.join('');
  }
}

function getLongestArray(obj) {
  // 获取对象的所有值 (数组)
  const values = Object.values(obj) as any[];
  
  // 使用 reduce 方法找出最长的数组
  const longestArray = values.reduce((longest, current) => {
    return current.length > longest.length ? current : longest;
  }, []);

  return longestArray;
}

class MetricTrendArea extends HighChartBuilder {

  public sortFn = (a, b) => 0;
  public series: any[];
  
  constructor(
    public dtType: DtType
  ) {
    super('spline');

    this
      .setChart('spacing', [0, 10, 0, 10])
      .setTheme([
        'rgba(80, 135, 236, 0.7)',
        'rgba(238, 117, 47, 0.7)',
        'rgba(104, 187, 196, 0.7)',
      ])
      .setXAxis('categories', ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'])
      .setTooltip('outside', false)
      .setTooltip('shared', true)
      .setTooltip('padding', 0)
      .setTooltip('shadow', false)
      .setLegend('verticalAlign', 'top')
      .setPlotOptions('spline', {
        turboThreshold: *********,
        marker: {
          enabled: true,
          radius: 2,
          symbol: 'circle'
        }
      });
  }

  override getOption(): this {
    const value = this;

    return {
      ...value,
      tooltip: {
        ...value.tooltip,
        formatter: tooltipFormatter(this)
      },
    }
  }

}

@Component({
  selector: 'app-metric-trend',
  template: `
    @if (option()) {
      <app-graph class="" [options]="option()" />
    }
  `,
  host: {
    'class': 'block relative h-full',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    GraphComponent,
  ],
})
export class MetricTrendComponent {
  
  value = input<QueryOutputVo>(null);
  dtType = input<DtType>(null);

  option = computed(() => {
    const series = this._extractSeries();
    const categories = this._extractCategories();
    const chart = new MetricTrendArea(this.dtType())
      .setXAxis('categories', categories)
      .setSeries(series);

    return chart.getOption();
  })


  private _extractCategories() {
    if (!this.value()) { return []; }

    const { headers, data } = this.value();
    const [dateFields] = getDateFields(headers);
    const numberFields = getNumberFields(headers);
    const newArr = data.map(item => {
      const [year, month, day] = item[dateFields].split('-').map(toNumber);
      const result = { year, month, day };
      numberFields.forEach(key => {
        result[key] = item[key];
      })
      return result;
    });

    let list = [];

    numberFields.forEach(() => {
      const values = groupBy(newArr, 'year');
      const longest = getLongestArray(values);

      if (longest.length > list.length) {
        list = _.cloneDeep(longest);
      }
    })

    return list.map(({ year, month, day }) => {
      switch(this.dtType()) {
        case 'dt':
          return `${_pad(month)}-${_pad(day)}`;
        case 'yw':
          return `第${month}周`;
        case 'ym':
          return `${month}月`;
      }
    });
  }

  
  private _extractSeries() {
    if (!this.value()) { return []; }
    
    const seriesList = [];
    const { headers, data } = this.value();
    const [dateFields] = getDateFields(headers);
    const numberFields = getNumberFields(headers);
    const newArr = data.map(item => {
      const [year, month, day] = item[dateFields].split('-').map(toNumber);
      const result = { year, month, day };

      numberFields.forEach(key => {
        result[key] = item[key];
        result[key+':ym_DIFF'] = item[key+':ym_DIFF'];
        result[key+':ym_DIFF_RATIO'] = item[key+':ym_DIFF_RATIO'];
        result[key+':ym_COMPARE_VALUE'] = item[key+':ym_COMPARE_VALUE'];
        result[key+':yw_DIFF'] = item[key+':yw_DIFF'];
        result[key+':yw_DIFF_RATIO'] = item[key+':yw_DIFF_RATIO'];
        result[key+':yw_COMPARE_VALUE'] = item[key+':yw_COMPARE_VALUE'];
      })

      return result;
    });

    // console.log(newArr);
    
    numberFields.forEach((key, index) => {
      const { aliasName, dataUnit, extendName } = headers[key];
      const values = groupBy(newArr, 'year');      

      // console.log('[values]', values);
      
      Object.keys(values).forEach((name, index) => {
        const series = new SeriesItem();
        const data = values[name].sort((a, b) => a.month - b.month).map(item => {
          return {
            y: toNumber(item[key]),
            yw: {
              diff: toNumber(item[key+':yw_DIFF']),
              ratio: toNumber(item[key+':yw_DIFF_RATIO']),
              compare: toNumber(item[key+':yw_COMPARE_VALUE']),
            },
            ym: {
              diff: toNumber(item[key+':ym_DIFF']),
              ratio: toNumber(item[key+':ym_DIFF_RATIO']),
              compare: toNumber(item[key+':ym_COMPARE_VALUE']),
            },
          }
        });
        
        // console.log('[vvvv]', values[name]);
        // console.log('[data]', data);

        if (dataUnit === '%') {
          series.isPercent = true;
        }

        series.name = `${name}-${aliasName}`;
        series.dataUnit = dataUnit;
        series.data = data;
        series.zIndex = 10 + index;
        // series.fillOpacity = 0.3;
          
        seriesList.push(series);
      })
    })

    seriesList.reverse();

    // console.log('[seriesList]', seriesList);
    
    return seriesList;
  }

}
