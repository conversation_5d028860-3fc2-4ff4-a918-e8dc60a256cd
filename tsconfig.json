/* To learn more about this file see: https://angular.io/config/tsconfig. */
{
  "compileOnSave": false,
  "compilerOptions": {
    "outDir": "./dist/out-tsc", // 编译后输出的目录，设置为 ./dist/out-tsc。
    "forceConsistentCasingInFileNames": true, // 强制文件名大小写一致。
    "strict": true, // 启用所有严格类型检查选项。
    "noImplicitOverride": false, // 是否允许隐式覆盖基类的方法（此项设置为 false，表示允许）。
    "noImplicitAny": false, // 禁止隐式的 any 类型（此项设置为 false，表示允许）。
    "exactOptionalPropertyTypes": false, // 是否精确检查可选属性的类型（此项设置为 false，表示不精确检查）。
    "noPropertyAccessFromIndexSignature": true, // 是否禁止通过索引签名访问属性（此项设置为 true，表示禁止）。
    "noImplicitReturns": false, // 是否检查函数是否所有代码路径都有返回值（此项设置为 false，表示不检查）。
    "noImplicitThis": false, // 是否检查 this 的隐式类型（此项设置为 false，表示不检查）。
    "noUncheckedIndexedAccess": false, // 是否检查索引访问的类型安全（此项设置为 false，表示不检查）。
    "noUnusedLocals": false, // 是否检查未使用的局部变量（此项设置为 true，表示检查）。
    "noFallthroughCasesInSwitch": true, // 是否禁止 switch 语句中的穿透情况（此项设置为 true，表示禁止）。
    "strictFunctionTypes": false, // 是否启用严格的函数类型检查（此项设置为 false，表示不严格）。
    "strictPropertyInitialization": false, // 是否启用严格的属性初始化检查（此项设置为 false，表示不严格）。
    "strictNullChecks": false, // 是否启用严格的 null 检查（此项设置为 false，表示不严格）。
    "esModuleInterop": true, // 启用 esModule 互操作性，以便与 CommonJS 模块更好地兼容。
    "sourceMap": true, // 是否生成源映射文件，以便调试。
    "declaration": false, // 是否生成 .d.ts 声明文件（此项设置为 false，表示不生成）。
    "experimentalDecorators": true, // 是否启用实验性的装饰器特性。
    // "emitDecoratorMetadata": true, // 用于Reflect.getMetadata('design:paramtypes'）
    "moduleResolution": "node", // 模块解析策略，设置为 node 表示使用 Node.js 风格的模块解析。
    "importHelpers": true, // 是否引入 TypeScript 帮助程序以减少生成的代码体积。
    "target": "ES2022", // 编译目标版本，设置为 ES2022。
    "module": "ES2022", // 编译后的模块系统，设置为 ES2022。
    "useDefineForClassFields": false, // 是否使用 define 来处理类字段（此项设置为 false，表示不使用）。
    "lib": [
      "ES2022",
      "dom"
    ],
     "types": ["leaflet"],
    "paths": {
      "@common/*": [
        "./src/app/@common/*"
      ],
      "@core/*": [
        "./src/app/core/*"
      ],
      "@api/*": [
        "./src/app/api/*"
      ],
      "@shared/*": [
        "./src/app/shared/*"
      ],
      "@views/*": [
        "./src/app/views/*"
      ]
    },
  },
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": false,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": true,
    "strictTemplates": true
  }
}
