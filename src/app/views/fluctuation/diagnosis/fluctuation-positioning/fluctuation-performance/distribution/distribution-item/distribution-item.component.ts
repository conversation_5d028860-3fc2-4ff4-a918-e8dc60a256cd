import { FormsModule } from '@angular/forms';
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, inject, input, signal } from '@angular/core';
import { combineLatest, debounceTime, finalize, startWith } from 'rxjs';
import { toObservable } from '@angular/core/rxjs-interop';
import { NzRadioModule } from 'ng-zorro-antd/radio';

import { formatMileageLabel } from '@common/function';
import { QueryEngineApiService } from '@api/query-engine';
import { FilterVo, QueryInputVo } from '@api/query-engine/model';
import { DiagnosisComponent } from '@views/fluctuation/diagnosis';
import { QueryEngineFormService } from '@common/service/query-engine';
import { LineSpinComponent } from '@shared/components/line-spin';
import { ChartComponent } from '@shared/components/chart';

import { FluctuationPerformanceComponent } from '../../fluctuation-performance.component';
import { BarChart } from './bar';


@Component({
  selector: 'app-distribution-item',
  template: `
    <div class="relative flex flex-col items-center justify-center h-100">
      @if (loading()) {
        <app-line-spin class="m-auto" />
      }
      @else {
        @if (option()) {
          <header class="relative z-10 flex items-center flex-wrap gap-1 w-full translate-y-6 max-2xl:translate-y-7">
            <span class="text-neutral-500 text-sm mr-auto">{{data().showName}}</span>

            <nz-radio-group [(ngModel)]="orderBy" nzSize="small">
              <label nz-radio class="text-xs!" nzValue="default">默认排序</label>
              <label nz-radio class="text-xs!" nzValue="value">按当前期数值倒序</label>
              <label nz-radio class="text-xs!" nzValue="diff">按差值倒序</label>
            </nz-radio-group>
            <div class="explain min-w-full min-h-4">{{tips()}}</div>
          </header>
          <app-charts class="w-full" [options]="option()" />
        }
        @else if (errorMessage()) {
          <span class="text-xs">{{errorMessage()}}</span>
        }
        @else {
          <span></span>
        }
      }
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    NzRadioModule,
    ChartComponent,
    LineSpinComponent,
  ],
})
export class DistributionItemComponent implements AfterViewInit {
  
  readonly formService = inject(QueryEngineFormService);
  readonly apiService = inject(QueryEngineApiService);
  readonly parent = inject(FluctuationPerformanceComponent);
  readonly root = inject(DiagnosisComponent);

  data = input<{
    title: string,
    showName: string,
    extendName: string,
    theme: string[],
    metrics: any
  }>(null);

  data$ = toObservable(this.data);
  loading = signal(false);
  errorMessage = signal<string>(null);
  orderBy = signal<'default' | 'value' | 'diff'>('default');
  orderBy$ = toObservable(this.orderBy);
  option = signal(null);

  tips = computed(() => {
    const { key } = this.data().metrics.find(item => item.primary);
    const includes = ['自有城际日均完单量', '自有市内日均完单量', '自有城际日均下单量', '自有市内日均下单量'].some(item => {
      return key.includes(item);
    });

    if (this.data().title === '里程对比') {
      if (includes && this.root.area() !== null) {
        return '说明：数据提示中会额外给出当前里程下的价格信息以及与DD的价格对比。';
      }
      return '说明：数据提示中会额外给出当前里程下的价格信息；';
    }
    return '';
  })

  ngAfterViewInit(): void {
    combineLatest([
      this.orderBy$,
      this.formService.form.valueChanges.pipe(startWith(null)),
      this.data$,
    ]).pipe(
      debounceTime(100)
    ).subscribe(() => {
      this.loading.set(true);
      this.query();
    })
  }

  query() {
    const input: QueryInputVo = this.formService.form.getRawValue();
    const { extendName, theme, metrics } = this.data();
    const dimensions = [{ id: null, extendName }];
    const body = { ...input, dimensions, metrics };

    if (this.data().extendName === 'c_scene_type') {
      body.orders = [{
        "extendName": "c_scene_type",
        "type":"desc",
        "source":2
      }];
    }

    body.filter.items = body.filter.items.concat({
      condition: "!=",
      extendName,
      conditionType: 2,
      value: [
        {
          value: "",
          key: ""
        }
      ]
    })

    body.queryType = 'compare';

    if (this.orderBy() === 'default') {
      body.orders = [{
        extendName,
        type: 'asc',
        source: 2
      }];
    }

    // 按当前期数值排序
    if (this.orderBy() === 'value') {
      body.metricsOrders = [{
        extendName: metrics.at(0).userDefExtendName,
        type: 'asc'
      }]
    }

    // 按涨跌幅排序
    if (this.orderBy() === 'diff') {
      body.metricsOrders = [{
        extendName: `${metrics.at(0).userDefExtendName}_DIFF`,
        type: 'asc'
      }]
    }

    if (this.root.area() === null) {
      body.metrics = body.metrics.filter(item => item.primary || item?.allRegionHidden !== true);
    }
    
    // console.log(this.data(), metrics.at(0));

    this.option.set(null);
    this.apiService.search(body, `distribution-${this.data().extendName}`).pipe(
      finalize(() => this.loading.set(false))
    ).subscribe(res => {
      if (res.data?.data) {
        try {
          const { dt, compareDt } = input;
          const primaryExtendName = metrics.at(0).userDefExtendName;
          const chart = new BarChart();

          if (this.data().extendName.includes('miage_intal')) {
            // chart.sortFn = sortCategoriesFn;
            chart.isMileage = true;
            (<any>chart.yAxis).axisLabel = {
              formatter: function (value) {
                return formatMileageLabel(value);
              }
            };
          }

          const metricsOrder = metrics.map(item => item.userDefExtendName);
          
          chart.color = theme;
          chart.dtType = this.formService.dtType.value;
          chart.primaryExtendName = primaryExtendName;
          chart.metricsOrder = metricsOrder;
          chart.init(res.data, dt, compareDt);
          
          this.option.set(chart?.getOption() || null);
        }
        catch (e: any) {
          console.log(e);
          this.errorMessage.set(e);
        }
      } else {
        this.errorMessage.set(res.error || res.message);
      }
    })
  }

}
