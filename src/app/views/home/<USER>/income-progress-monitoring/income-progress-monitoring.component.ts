import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, inject, signal } from '@angular/core'
import { FormsModule } from '@angular/forms'
import { CaerusApiService } from '@api/caerus'
import { QueryEngineApiService } from '@api/query-engine'
import { QueryOutputVo } from '@api/query-engine/model'
import { BuriedPointService, LegendControlService, LegendItemClickHandler } from '@common/service'
import { QueryEngineFormService } from '@common/service/query-engine'
import { HeadingComponent } from '@shared/components/heading'
import { RadioModule } from '@shared/modules/headless'
import { IconTransferComponent } from '@shared/modules/icons'
import { groupBy, find } from 'lodash'
import { combineLatest, finalize } from 'rxjs'
import { ProgressTrend } from '../order-tracking-overview/lib'
import { LineSpinComponent } from '@shared/components/line-spin'
import { GraphComponent } from '@shared/components/graph'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { HomeCardComponent } from '../home-card/home-card.component'
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox'
import { SwitchMap } from '@common/decorator'
import { DatePipe } from '@angular/common'
import { HomeService } from '@views/home/<USER>'
import { getMonthFirstAndLastDay, getMonthFirstAndNowDay } from '@common/class'
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop'
import { toNumber } from '@common/function'
import { PAGE_NAME } from '@common/directive'

@Component({
  selector: 'app-income-progress-monitoring',
  imports: [
    HeadingComponent,
    FormsModule,
    IconTransferComponent,
    RadioModule,
    LineSpinComponent,
    GraphComponent,
    NzPopoverModule,
    HomeCardComponent,
    NzCheckboxModule,
  ],
  templateUrl: './income-progress-monitoring.component.html',
  host: {
    class: 'block px-5 pb-5',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [LegendControlService, QueryEngineFormService, DatePipe],
})
export class IncomeProgressMonitoringComponent implements AfterViewInit {
  page_name = inject(PAGE_NAME)
  readonly buriedPointService = inject(BuriedPointService)
  readonly apiService = inject(CaerusApiService)
  readonly formService = inject(QueryEngineFormService)
  readonly queryService = inject(QueryEngineApiService)
  readonly legendControlService = inject(LegendControlService)
  readonly datePipe = inject(DatePipe)
  readonly destroyRef = inject(DestroyRef)
  readonly service = inject(HomeService)

  readonly radioMap = {
    day: ':dt_DIFF_RATIO',
    week: ':yw_DIFF_RATIO',
    month: ':ym_DIFF_RATIO',
    year: ':year_DIFF_RATIO',
  }

  currentMetrics = signal([])
  list = signal([])
  allMetrics = signal(null)
  config = signal(null)
  c_ord_type = signal(null)
  c_firlev_channel = signal(null)
  c_firlev_channel$ = toObservable(this.c_firlev_channel)
  c_seclev_channel = signal(null)
  is_site_pkg = signal(null)
  type = signal('month')
  loading = signal(false)
  cardLoading = signal(false)
  option = signal(null)
  errorMessage = signal(null)
  index = signal(1)
  tooltip = [
    `说明: 目标及目标完成率按<span class="text-blue-500">财务口径</span>数据计算`,
    null,
    `说明: 目标及目标完成率按<span class="text-blue-500">财务口径</span>数据计算`,
    null,
  ]
  dt = signal(null)
  bizType = signal('1')

  chartMetrics = computed(() => {
    if (!this.allMetrics()) {
      return []
    }
    const dimensions = this.allMetrics()[`income_process_monitoring_${this.type()}_tab1`]
    const groupDimension = groupBy(dimensions.subMetric, 'tagName')

    const arr = Object.keys(groupDimension).map((dimension, index) => {
      return {
        showName: dimension,
        aliasName: groupDimension[dimension][0].aliasName,
        bizExpression: groupDimension[dimension][0].bizExpression,
        value: groupDimension[dimension].map(d => {
          return {
            extendName: d.extendName,
            displayOrder: d.displayOrder,
          }
        }),
        checked: index === this.index() - 1,
      }
    })
    return arr
  })

  seclevChannelOptions = computed(() => {
    const items =
      this.config()?.c_seclev_channel?.filter(item => {
        const { value } = this.c_firlev_channel() || {}

        if (value) {
          return item.relValue === value
        }

        return true
      }) || []

    return items
  })

  ngAfterViewInit(): void {
    this.fetchMetrics()
    this.fetchConfig()
    this.subscribeToMonthChange()
    this._subscribeToFirlevChannelChange()
  }

  private _subscribeToFirlevChannelChange() {
    this.c_firlev_channel$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      this.c_seclev_channel.set(null)
    })
  }

  buriedPoint(name, value) {
    this.buriedPointService.addStat('dida_dpm_caerus_home_finance_fliter_click', {
      page_name: this.page_name,
      fliter_name: name,
      fliter_options: value,
    })
  }

  dealBuriedPoint(name, value) {
    const _value = value ? (name === '二级业务渠道' ? value.key : value.showValue) : '全部'
    this.buriedPoint(name, _value)
  }

  subscribeToMonthChange() {
    combineLatest([this.service.month$, this.service.updateTimeResource$])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(([value, updateTime]) => {
        if (!updateTime) {
          return
        }
        const month = this.datePipe.transform(value, 'yyyy-MM-dd')
        const [startTime] = getMonthFirstAndNowDay(month)
        this.dt.set({
          startTime,
          endTime: updateTime,
        })
        this.getCardData()
        // this.getChartData()
      })
  }

  dealGroupDimension() {
    this.loading.set(true)
    const dimensions = this.allMetrics()[`income_process_monitoring_${this.type()}_tab1`]
    const groupDimension = groupBy(
      dimensions.subMetric.filter(d => d.recommend === 1),
      'tagName'
    )

    const arr = Object.values(groupDimension).map((item: any, index) => {
      const extendNames = item.map(sub => ({
        extendName: sub.extendName,
        recommend: sub.recommend,
        displayOrder: sub.displayOrder,
      }))
      const order4 = find(item, ['displayOrder', 4])
      return {
        aliasName: item[0].aliasName,
        bizExpression: item[0].bizExpression,
        showName: Object.keys(groupDimension)[index],
        extendNames,
        twobizExpression: order4 && order4.recommend === 1,
        twobizExpressionName: ['财务口径', '业务口径'],
      }
    })

    this.currentMetrics.set(arr)
    this.getCardData()
  }

  fetchMetrics() {
    this.apiService.fetchMetricsConfig('cockpit_finance_income_process_monitoring').subscribe(res => {
      if (res.data) {
        console.log('metric-', res.data)
        this.allMetrics.set(res.data)
        this.dealGroupDimension()
      } else {
        this.currentMetrics.set([])
      }
    })
  }

  changeItem() {
    this.getChartData()
  }

  changeCard(index) {
    if (this.list().length !== 0) {
      this.buriedPointService.addStat('dida_dpm_caerus_home_financecard_click', {
        page_name: this.page_name,
        indicator_name: this.list()[index - 1].extendNames[0].extendName,
      })
    }
    this.index.set(index)
    this.bizType.set('1')
    this.getChartData()
  }

  getFilterQuery() {
    const items = [this.c_ord_type(), this.c_firlev_channel(), this.c_seclev_channel(), this.is_site_pkg()]
      .filter(a => a)
      .map(item => {
        return {
          conditionType: 2,
          condition: '=',
          id: null,
          extendName: item.extendName,
          value: [
            {
              key: item.key,
              value: item.value,
            },
          ],
          valueType: null,
        }
      })
    return items
  }

  @SwitchMap()
  getChartData() {
    if (this.chartMetrics().length === 0 || !this.dt().endTime) {
      return
    }
    this.loading.set(true)
    const body = this.formService.value()
    const [, end] = getMonthFirstAndLastDay(this.dt().endTime)
    body.dt = {
      startTime: this.dt().startTime,
      endTime: end,
    }

    body.dataFillConfig = {
      open: 0,
      fillRangeType: 1,
      fillValue: 1,
    }
    body.dimensions = [
      {
        id: null,
        extendName: 'dt',
      },
    ]

    body.filter = {
      items: this.getFilterQuery(),
      type: null,
    }

    const checkedMetrics = this.chartMetrics().filter(c => c.checked)
    const metrics = checkedMetrics.reduce((acc, c) => acc.concat(c.value), [])

    if (this.bizType() === '1') {
      body.metrics = metrics.filter(m => {
        return m.displayOrder !== 4
      })
    } else {
      body.metrics = metrics.filter(m => {
        return m.displayOrder === 4
      })
    }

    this.legendControlService.reset()
    return this.queryService
      .search(body, 'income-chart')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        if (res.data) {
          if (this.bizType() === '1') {
            const obj = {}
            checkedMetrics.forEach(c => {
              const order2 = find(c.value, ['displayOrder', 2])
              if (order2) {
                const order1 = find(c.value, ['displayOrder', 1])
                obj[order1.extendName] = order2.extendName
              }
            })
            const result = {}
            Object.keys(obj).forEach(o => {
              result[`${res.data.headers[o].aliasName}`] = []
            })
            res.data.data.forEach(d => {
              Object.keys(obj).forEach(o => {
                result[`${res.data.headers[o].aliasName}`].push(`${toNumber(d[o]) - toNumber(d[obj[o]])}`)
              })
            })
            this._setChartData(res.data, result)
          } else {
            this._setChartData(res.data, null)
          }
        } else {
          this.option.set(null)
          this.errorMessage.set(res.message)
        }
      })
  }

  fetchConfig() {
    this.apiService.fetchDimensionConfig('cockpit_finance_income_process_dim').subscribe(res => {
      if (res.data) {
        const obj = {}
        res.data.top_filter.forEach(f => {
          obj[f.extendName] = f.values
        })
        console.log('dimension-', obj)
        this.config.set(obj)
      } else {
        this.config.set(null)
      }
    })
  }

  changeType() {
    if (!this.allMetrics()) {
      return
    }
    this.dealGroupDimension()
  }

  @SwitchMap()
  private getCardData() {
    const body = this.formService.value()
    const startTime = this.dt().startTime
    const endTime = this.dt().endTime

    if (this.type() === 'month') {
      body.dtType = 'ym'
      body.dt = {
        startTime: startTime.split('-').slice(0, 2).join('-'),
        endTime: endTime.split('-').slice(0, 2).join('-'),
      }
    } else {
      body.dt = {
        startTime: endTime,
        endTime,
      }
    }

    body.dimensions = [
      {
        id: null,
        extendName: this.type() === 'day' ? 'dt' : 'ym',
        predefineCompareType: this.type() === 'day' ? ['yw', 'dt'] : ['ym', 'year'],
      },
    ]

    body.metrics = this.currentMetrics()
      .reduce((arr, m) => arr.concat(m.extendNames.map(ex => ex.extendName)), [])
      .map(extendName => ({ extendName }))

    body.dataFillConfig = {
      open: 1,
      fillRangeType: 2,
      fillValue: 1,
    }

    body.filter = {
      items: this.getFilterQuery(),
      type: null,
    }

    this.cardLoading.set(true)

    return this.queryService
      .search(body, 'income-card')
      .pipe(finalize(() => this.cardLoading.set(false)))
      .subscribe(res => {
        console.log('income-card', res.data)
        if (res.data && res.data.data.length !== 0) {
          const _data = res.data.data[0]
          this.currentMetrics().forEach(m => {
            m.extendNames.forEach(ex => {
              ex.data = _data[ex.extendName] || 0
              if (ex.displayOrder === 1 || ex.displayOrder === 4) {
                // if (this.type() === 'day') {
                ex.dt = _data[`${ex.extendName}${this.radioMap.day}`]
                ex.yw = _data[`${ex.extendName}${this.radioMap.week}`]
                // } else {
                ex.ym = _data[`${ex.extendName}${this.radioMap.month}`]
                ex.year = _data[`${ex.extendName}${this.radioMap.year}`]
                // }
              }
            })
            m.diff = m.extendNames[1] ? m.extendNames[0]?.data - m.extendNames[1]?.data : 0
          })
        } else {
          this.currentMetrics().forEach(m => {
            m.extendNames.forEach(ex => {
              ex.data = null
              if (ex.displayOrder === 1 || ex.displayOrder === 4) {
                // if (this.type() === 'day') {
                ex.dt = null
                ex.yw = null
                // } else {
                ex.ym = null
                ex.year = null
                // }
              }
            })
            m.diff = null
          })
        }
        this.list.set(this.currentMetrics())
      })
  }

  private _setChartData(data: QueryOutputVo, diffData) {
    try {
      const chart = new ProgressTrend({ ...data, diffData })
      const legendItemClick = LegendItemClickHandler(this.legendControlService)
      chart.plotOptions.series.events = { legendItemClick }
      this.option.set(chart?.getOption() || null)
    } catch (e) {
      this.option.set(null)
      this.errorMessage.set(e)
      console.error(e)
    }
  }

  reRender() {
    this.getCardData()
    this.getChartData()
  }
}
