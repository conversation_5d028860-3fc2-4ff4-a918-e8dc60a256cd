<nz-spin [nzSpinning]="metricsDataResource.isLoading()">
  <div class="py-3 max-2xl:py-1">
    <strong class="text-orange-400 whitespace-nowrap">乘客侧</strong>
    <header class="flex flex-nowrap gap-x-3">
      <app-dimension-filter label="乘客四类" [(ngModel)]="c_pass_fourca_user_type" [options]="fourcaUserTypeOptions()" />
    </header>
  </div>
  <app-indicator-card-group class="group relative h-60 p-3.5 py-0 rounded-2xl card-theme orange">
    <ExpandIcon class="absolute! z-10 right-3 top-3 expand-btn" iconBtn nz-tooltip="查看" (click)="openMetricsDetail()" />

    @for (items of metricsList(); track $index) {
      <app-indicator-tabs-card #tabCardRef [metrics]="items" [valueMap]="metricsValueMap()">
        @if (tabCardRef.metric() && tabCardRef.valueMap()) {
          <app-indicator-content>
            @if (tabCardRef.hasTabs()) {
              <app-radio-group [(ngModel)]="tabCardRef.tabIndex" class="flex justify-center">
                @for (tab of tabCardRef.tabs(); track $index) {
                  <ng-template #metricTitleTemplate>
                    {{tab.showName}} <span class="text-xs opacity-30 px-1">({{tab.aliasName}})</span>
                  </ng-template>
                  <app-radio
                    [value]="$index"
                    class="tab-radio"
                    activeClass="active"
                    nz-popover
                    [nzPopoverTitle]="metricTitleTemplate"
                    [nzPopoverContent]="contentTemplate"
                  >
                    <ng-template #contentTemplate>
                      <div [innerHTML]="tab?.bizExpression"></div>
                    </ng-template>
                    {{tab.tagName | replaceBy: replaceFn}}
                  </app-radio>
                }
              </app-radio-group>
            }
            @else {
              <app-indicator-header class="justify-center h-5.5">
                <ng-template #metricsTitleTemplate>
                  {{tabCardRef.metric().showName}} <span class="text-xs opacity-30 px-1">({{tabCardRef.metric().aliasName}})</span>
                </ng-template>
                <app-indicator-title
                  [nzPopoverTitle]="metricsTitleTemplate"
                  [nzPopoverContent]="contentTemplate"
                >
                  <ng-template #contentTemplate>
                    <div [innerHTML]="tabCardRef.metric().bizExpression"></div>
                  </ng-template>
                  {{tabCardRef.metric().card}}
                </app-indicator-title>
              </app-indicator-header>
            }
            
            <app-indicator-value class="mt-auto mx-auto" fontSize="text-xl" [nullValue]="'-'" [value]="tabCardRef.value()?.value | increment | async">
              <span class="absolute inline-flex items-start top-0 right-0 translate-x-full">
                <app-indicator-subtitle>{{tabCardRef.metric().bizTag}}</app-indicator-subtitle>
              </span>
            </app-indicator-value>

            <app-indicator-compare-group vertical gap="gap-1" class="scale-80">
              <app-indicator-compare label="月环比" [value]="tabCardRef.value()?.ym_DIFF_RATIO" />
              <app-indicator-compare label="年同比" [value]="tabCardRef.value()?.year_DIFF_RATIO" />
            </app-indicator-compare-group>

            @let diffState = (
              tabCardRef.metric().compareDiff === 'target'
                ? tabCardRef.target()?.value - tabCardRef.value()?.value >= 0
                : tabCardRef.timeDiffRate() >= 0
            );

            @let diffLabel = (
              tabCardRef.metric().compareDiff === 'target' 
                ? (
                  diffState 
                    ? '<span class="text-green-500">(较目标少{n})</span>' 
                    : '<span class="text-red-600">(较目标多{n})</span>'
                )
                : (
                  diffState 
                    ? '<span class="text-red-600">(较时间进度快'+tabCardRef.timeDiffRate()+'pp)</span>' 
                    : '<span class="text-green-500">(较时间进度慢'+tabCardRef.timeDiffRate()+'pp)</span>'
                )
            );

            <app-indicator-progress
              [diffLabelDisabled]="tabCardRef.metric()?.dtQuery"
              [nullValuePlaceholder]="''"
              [label]="tabCardRef.rateMetric()?.showName || ''"
              [targetLabel]="tabCardRef.targetMetric()?.showName || '暂无目标'"
              [diffLabel]="diffLabel"
              [value]="tabCardRef.value()?.value"
              [total]="tabCardRef.target()?.value"
              [fractionDigits]="2"
            />
          </app-indicator-content>
        }
      </app-indicator-tabs-card>
    }
  </app-indicator-card-group>
</nz-spin>
