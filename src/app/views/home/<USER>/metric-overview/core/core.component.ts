import { Async<PERSON>ip<PERSON>, DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, DestroyRef, effect, inject, signal, ViewContainerRef } from '@angular/core';
import { rxResource, takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { subYears, format } from 'date-fns';
import { FormsModule } from '@angular/forms';
import { NzSpinComponent } from 'ng-zorro-antd/spin';
import { filter, map, of, tap } from 'rxjs';

import { isDev, trace } from '@common/const';
import { BuriedPointService, UserService } from '@common/service';
import { QueryInputVo } from '@api/query-engine/model';
import { QueryEngineApiService } from '@api/query-engine';
import { MetricsMenuVo } from '@api/caerus/model';
import { CaerusApiService } from '@api/caerus';

import { ModalService } from '@core/dialog';
import { RadioModule } from '@shared/modules/headless';
import { groupBy, isEmpty, isNotNull, isNull, toNumber } from '@common/function';
import { GraphMiniAreaComponent } from '@shared/components/graph';
import { IndicatorModule } from '@shared/components/indicator';
import { IconCardComponent, IconCollectComponent, IconCooperationComponent, IconExpandComponent, IconMapArrowRightComponent, IconTaxiComponent, IconUserFillComponent } from '@shared/modules/icons';
import { IncrementPipe } from '@shared/pipes/increment';
import { LineSpinComponent } from '@shared/components/line-spin';
import { ReplaceByPipe } from '@shared/pipes/replace-by';
import { HomeService, HomeComponent } from '@views/home';

import { MetricOverviewComponent } from '../metric-overview.component';
import { MetricsDetailComponent } from '../metrics-detail';


@Component({
  selector: 'app-core',
  templateUrl: './core.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    AsyncPipe,
    FormsModule,
    NzSpinComponent,
    IndicatorModule,
    RadioModule,
    LineSpinComponent,
    GraphMiniAreaComponent,
    IconExpandComponent,
    IconMapArrowRightComponent,
    IconCooperationComponent,
    IconUserFillComponent,
    IconTaxiComponent,
    IconCollectComponent,
    IconCardComponent,
    IncrementPipe,
    ReplaceByPipe,
  ],
  providers: [
    DatePipe,
  ]
})
export class CoreComponent {

  readonly datePipe = inject(DatePipe);
  readonly cdr = inject(ChangeDetectorRef);
  readonly viewContainerRef = inject(ViewContainerRef);
  readonly modalService = inject(ModalService);
  readonly service = inject(HomeService);
  readonly caerusApiService = inject(CaerusApiService);
  readonly buriedPointService = inject(BuriedPointService);
  readonly apiService = inject(QueryEngineApiService);
  readonly destroyRef = inject(DestroyRef);
  readonly userService = inject(UserService);
  readonly parent = inject(MetricOverviewComponent);
  readonly root = inject(HomeComponent);

  valueMap = new Map();
  trendMap = new Map();
  metricsValueMap = signal(null);
  metricsTrendMap = signal(null);
  hasPermission = signal(isDev() ? true : false);
  
  metricsConfigResource = rxResource({
    loader: () => this.caerusApiService.fetchMetricsConfigV2('cockpit_top_metrics_v3').pipe(
      map(res => res.data['core_overview_pop1']?.subMetric?.filter(item => item.recommend === 1)),
      // tap(res => { console.log(res); })
    )
  });

  metricsDataResource = rxResource({
    request: () => this.metricsDataBody(),
    loader: ({ request }) => isEmpty(request.metrics) 
      ? of(null)
      : this.caerusApiService.getCoreResult(request, 'common-core-result').pipe(
        tap(res => {
          if (res.status !== '00000') {
            throw res.message;
          }
        }),
        map(res => res.data?.data?.at(0)),
        // tap(value => {
        //   // console.log('[value]', value);
          
        //   Object.keys(value).forEach(key => {
        //     if (key !== 'ym' && key.indexOf(':') === -1) {
        //       const target = this.valueMap.get(key);
        //       this.valueMap.set(key, { ...target, value: parseFloat(value[key]) || null });
        //     }
        //     else if (key.indexOf(':') > -1) {
        //       const [k, v] = key.split(':');
        //       const target = this.valueMap.get(k);

        //       this.valueMap.set(k, {  ...target, [v]: parseFloat(value[key]) || null });
        //     }
        //   })

        //   this.metricsValueMap.set(this.valueMap);
        // })
      )
  })

  specialMetricsDataResource = rxResource({
    request: () => this.specialMetricsDataBody(),
    loader: ({ request }) => (isNull(request) || isEmpty(request?.metrics))
      ? of(null)
      : this.caerusApiService.getKeyProcessDt(request, 'special-core-result').pipe(
        tap(res => {
          if (res.status !== '00000') {
            throw res.message;
          }
        }),
        map(res => res.data?.data?.at(0)),
        // tap(value => {
        //   // console.log('[value]', value);
          
        //   Object.keys(value).forEach(key => {
        //     if (key !== 'ym' && key.indexOf(':') === -1) {
        //       const target = this.valueMap.get(key);
        //       this.valueMap.set(key, { ...target, value: parseFloat(value[key]) || null });
        //     }
        //     else if (key.indexOf(':') > -1) {
        //       const [k, v] = key.split(':');
        //       const target = this.valueMap.get(k);

        //       this.valueMap.set(k, {  ...target, [v]: parseFloat(value[key]) || null });
        //     }
        //   })

        //   this.metricsValueMap.set(this.valueMap);
        // })
      )
  })

  metricsTrendResource = rxResource({
    request: () => this.metricsTrendBody(),
    loader: ({ request }) => isEmpty(request.metrics) 
      ? of(null) 
      : this.caerusApiService.getCoreResultTrendAll(request, 'common-metrics-trend').pipe(
        tap(res => {
          if (res.status !== '00000') {
            throw res.message;
          }
        }),
        map(res => res.data.data),
      )
  })

  metricsDataBody = computed(() => {
    const month = this.datePipe.transform(this.service.month(), 'yyyy-MM');
    const dt = { startTime: month, endTime: month };
    const dimensions = [{ id: null, extendName: 'ym', predefineCompareType: ['ym', 'year'] }];
    const metrics = this.#common_metrics()?.map(item => {
      return {
        ...item,
        filter: this.#metric_filters(),
      };
    });

    const body = {
      dt,
      dtType: 'ym',
      metrics,
      dimensions,
      filter: this.#filters(),
      scene: 4,
      dataFillConfig: { open: 1, fillRangeType: 1, fillValue: 1 },
      queryType: 'compare',
      accelerateFilter: true,
    } as QueryInputVo;

    return body;
  })

  specialMetricsDataBody = computed(() => {
    const month = this.datePipe.transform(this.service.updateTime(), 'yyyy-MM-dd');
    const dt = { startTime: month, endTime: month };
    const dimensions = [{ id: null, extendName: 'dt', predefineCompareType: ['ym', 'year'] }];
    const metrics = this.#special_metrics()?.map(item => {
      return {
        ...item,
        filter: this.#metric_filters(),
      };
    });

    const body = {
      dt,
      dtType: 'dt',
      metrics,
      dimensions,
      filter: this.#filters(),
      scene: 4,
      dataFillConfig: { open: 1, fillRangeType: 1, fillValue: 1 },
      queryType: 'compare',
      accelerateFilter: true,
    } as QueryInputVo;

    if (!month) {
      return null;
    }

    return body;
  })

  metricsTrendBody = computed(() => {
    const startTime = format(subYears(this.service.month(), 2).setMonth(0), 'yyyy-MM');
    const endTime = format(this.service.month(), 'yyyy-MM');
    const dt = { startTime, endTime }; // 近2年
    const dimensions = [{ id: null, extendName: 'ym' }];
    const metrics = this.#commonTrendMetrics()?.map(item => {
      return {
        ...item,
        filter: this.#metric_filters(),
      };
    });
    
    return {
      dt,
      dtType: 'ym',
      metrics,
      dimensions,
      filter: this.#filters(),
      scene: 4,
      dataFillConfig: { open: 1, fillRangeType: 1, fillValue: 1 },
      queryType: 'compare',
      accelerateFilter: true,
    } as QueryInputVo;
  })

  #common_metrics = computed(() => {    
    return this.metricsConfigResource.value()
      ?.filter(item => item.recommend === 1)
      ?.filter(item => !item?.dtQuery)
      ?.map(({ extendName, showName, aliasName }) => ({ 
        extendName, showName, 
        userDefExtendName: 'COMPARE_'+extendName, 
        userDefAliasName: aliasName,
      }));
  });

  #special_metrics = computed(() => {    
    return this.metricsConfigResource.value()
      ?.filter((item: any) => item.recommend === 1)
      ?.filter(item => item?.dtQuery === true)
      ?.map(({ extendName, showName, aliasName }) => ({ 
        extendName, showName, 
        userDefExtendName: 'COMPARE_'+extendName, 
        userDefAliasName: aliasName,
      }));
  });

  #commonTrendMetrics = computed(() => {    
    return this.metricsConfigResource.value()
      ?.filter(item => item.recommend === 1)
      ?.filter(item => item.metricsType === 'value')
      // ?.filter(item => !item?.dtQuery)
      ?.map(({ extendName, showName, aliasName }) => ({ 
        extendName, showName, 
        userDefExtendName: 'COMPARE_'+extendName, 
        userDefAliasName: aliasName
      }));
  });

  metricsList = computed(() => {
    if (this.metricsConfigResource.value()) {
      const res = this.metricsConfigResource.value();
      const groupObj = groupBy(res, 'tagName');
      const groupMap = new Map();
      const arr = Object.keys(groupObj);
      
      arr.forEach(key => {
        const item = groupObj[key];
        const { card } = item.at(0);

        if (groupMap.has(card)) {
          groupMap.set(card, groupMap.get(card).concat([item]));
        } else {
          groupMap.set(card, [item]);
        }
      })

      const items = [...groupMap.values()].map((c: any[]) => {
        return c.map((m) => {
          return m.filter(item => {
            if (item?.auth === true) {
              return this.hasPermission()
            }
            return true;
          })
        })
      })
      .filter(item => {
        return item.at(0).length > 0;
      })
      
      // console.log('[items]', items);
      
      return items;
    }
    return [];
  })

  #metric_filters = computed(() => {
    const items = [
      this.parent.c_seclev_channel(),
    ].filter(isNotNull);

    return {
      items, 
      type: null
    }
  })

  #filters = computed(() => {
    const items = [
      this.parent.c_ord_type(),
      this.parent.c_firlev_channel(),
      this._globalAreaFilter(),
    ].filter(isNotNull);

    return { 
      items, 
      type: null
    }
  })

  constructor() {
    effect(() => {
      if (
        this.metricsDataResource.value() ||
        this.specialMetricsDataResource.value()
      ) {
        const value = {
          ...this.metricsDataResource.value() || {},
          ...this.specialMetricsDataResource.value() || {},
        };

        this.valueMap.clear();
        Object.keys(value).forEach(key => {
          if (key !== 'ym' && key.indexOf(':') === -1) {
            const target = this.valueMap.get(key);
            this.valueMap.set(key, { ...target, value: parseFloat(value[key]) || null });
          }
          else if (key.indexOf(':') > -1) {
            const [k, v] = key.split(':');
            const target = this.valueMap.get(k);

            this.valueMap.set(k, { ...target, [v]: parseFloat(value[key]) || null });
          }
        })

        this.metricsValueMap.set(null);
        setTimeout(() => {
          this.metricsValueMap.set(this.valueMap);
        }, 0)
      }
    })
    
    effect(() => {
      if (
        this.metricsDataResource.value() &&
        this.metricsTrendResource.value() &&
        true
      ) {
        const values = [
          this.metricsTrendResource.value(),
        ].flat(1);
        
        this.trendMap.forEach((value) => {
          value.trendVo = [];
        })
        
        values.forEach(item => {
          if (item['ym']) {
            const [year, month] = item['ym'].split('-').map(toNumber);
            Object.keys(item).filter(key => key !== 'ym').forEach(key => {
              const target = this.trendMap.get(key);
              const trendVo = target?.trendVo || [];
              const value = parseFloat(item[key]) || null;

              trendVo.push({ year, month, value })
              this.trendMap.set(key, {  ...target, trendVo });
            });
          }
        })

        this.metricsTrendMap.set(null);

        setTimeout(() => {
          this.metricsTrendMap.set(this.trendMap);
          this.cdr.markForCheck();
        }, 0)
      }
    })
  }

  ngAfterViewInit(): void {
    this._subscribeToRolesChange();
  }

  private _subscribeToRolesChange() {
    this.userService.role$.pipe(filter(isNotNull), takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      const hasPermission = this.userService.hasRole('Caerus财务盯盘查看权限');

      this.hasPermission.set(hasPermission);
    });
  }
  
  private _globalAreaFilter() {
    if (this.parent.area()) {
      const { key, value, extendName } = this.parent.area();
      
      return {
        conditionType: 2,
        condition: "=",
        id: null,
        extendName,
        value: [{ key, value }],
        valueType: null
      };
    }
    return null;
  }

  openMetricsDetail(data: MetricsMenuVo) {
    console.clear();
    trace(`埋点上报：大盘核心指标总览-卡片右上角按钮点击`, {
      page_name: this.root.page_name,
      indicator_name: data.extendName,
    });

    this.buriedPointService.addStat('dida_dpm_caerus_home_card_enlarge_click', {
      page_name: this.root.page_name,
      indicator_name: data.extendName,
    });

    this.modalService.open(MetricsDetailComponent, { 
      viewContainerRef: this.viewContainerRef,
      data: {
        metrics: [data],
        filters: {
          area: this.parent.area()?.value,
          c_firlev_channel: this.parent.c_firlev_channel(),
          c_seclev_channel: this.parent.c_seclev_channel(),
          c_ord_type: this.parent.c_ord_type(),
        }
      }
    });
  }
  
}
