import { As<PERSON><PERSON>ip<PERSON>, DecimalPipe, Ng<PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, effect, ElementRef, inject, linkedSignal, signal, viewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { Subscription, combineLatest, debounceTime, finalize, startWith } from 'rxjs';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import * as _ from 'lodash';


import { groupBy } from '@common/function';
import { trace } from '@common/const';
import { SwitchMap } from '@common/decorator';
import { BuriedPointService } from '@common/service';
import { ResultBody } from '@common/interface';
import { IntersectionDirective, PAGE_NAME } from '@common/directive';
import { QueryEngineApiService } from '@api/query-engine';
import { QueryInputVo, QueryOutputHeaderVo, QueryOutputVo } from '@api/query-engine/model';
import { CaerusApiService } from '@api/caerus';
import { QueryEngineFormService } from '@common/service/query-engine';
import { IconSuccessFillComponent } from '@shared/modules/icons';
import { RadioModule } from '@shared/modules/headless';
import { ValueFormatter } from '@shared/components/value-formatter';
import { LineSpinComponent } from '@shared/components/line-spin';
import { GraphComponent } from '@shared/components/graph';
import { IncrementPipe } from '@shared/pipes/increment';
import { CarouselModule } from '@shared/modules/carousel';

import { FluctuationPositioningComponent } from '../fluctuation-positioning.component';
import { LineMultiXAxis, mergeResult, processDataGroup, updateDataItems } from '../../fluctuation-diagnosis/shared/lib';
import { DiagnosisComponent } from '../../diagnosis.component';
import { DistributionComponent } from './distribution';


interface MetricsConfig {
  aliasName: string;
  barMetrics?: Array<{
    aliasName: string;
    extendName: string;
    filter: {
      type: unknown,
      items: any[]
    };
    userDefExtendName: string;
  }>;
  extendName: string;
  bizExpression: string;
  key: string;
}


@Component({
  selector: 'app-fluctuation-performance',
  templateUrl: './fluctuation-performance.component.html',
  styleUrl: './fluctuation-performance.component.css',
  host: {
    id: 'anchor-fluctuation-performance'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    AsyncPipe,
    DecimalPipe,
    IncrementPipe,
    NgTemplateOutlet,
    FormsModule,
    NzSwitchModule,
    NzCheckboxModule,
    NzPopoverModule,
    RadioModule,
    CarouselModule,
    IconSuccessFillComponent,
    DistributionComponent,
    LineSpinComponent,
    ValueFormatter,
    GraphComponent,
    IntersectionDirective,
  ],
})
export class FluctuationPerformanceComponent implements AfterViewInit {

  readonly page_name = inject(PAGE_NAME);
  readonly destroyRef = inject(DestroyRef);
  readonly formService = inject(QueryEngineFormService);
  readonly apiService = inject(QueryEngineApiService);
  readonly caerusApiService = inject(CaerusApiService);
  readonly buriedPointService = inject(BuriedPointService);
  readonly parent = inject(FluctuationPositioningComponent);
  readonly root = inject(DiagnosisComponent);
  readonly message = inject(NzMessageService);

  private _finishMetrics = [];
  private _bookMetrics = [];
  
  anchor = viewChild<ElementRef>('metricSelect');
  querying1 = signal(false);
  querying2 = signal(false);
  noData = signal(false);
  errorMessage = signal<string>(null);
  selectedLegends = signal([]);
  source2$ = toObservable(this.selectedLegends);
  searchSubscription: Subscription;

  metric = linkedSignal<any, any>({
    source: () => this.parent.view(),
    computation: (source, previous) => {
      switch (source) {
        case 'book':
          return this.bookMetrics().find(item => _.isEqual(item, previous?.value)) || this.bookMetrics()[0];
        case 'finish':
          return this.finishMetrics().find(item => _.isEqual(item, previous?.value)) || this.finishMetrics()[0];
      }
    }
  });

  // metric$ = toObservable(this.metric);
  isNotDauMetric = computed(() => {
    if (this.metric()) {
      const { key: showName } = this.metric();

      return !(/DAU/.test(showName));
    }
    return false;
  })

  compareTotal = linkedSignal({
    source: () => this.targetVisible(),
    computation: (state) => {
      /** 如果对比大盘按钮不可见，则对比大盘应为`false` */
      if (state === false) {
        return false;
      }
      return false;
    }
  });

  compareTotal$ = toObservable(this.compareTotal);
  allChannel = signal(null);
  otherChannel = signal(null);

  finishMetrics = signal([]);
  bookMetrics = signal([]);
  finishLegends = signal([]);
  bookLegends = signal([]);

  /** 对比大盘按钮是否可见 */
  targetVisible = computed(() => {
    if (this.root.areaForm$()) {
      return this.root.areaForm$().extendName !== null;
    }

    return false;
  });

  option = computed(() => {
    let data: QueryOutputVo;
    let chart: LineMultiXAxis = null;

    data = mergeResult(data, this.allChannel());
    data = mergeResult(data, this.otherChannel());
    
    if (data) {
      chart = new LineMultiXAxis(data, true);
      chart.dtType = this.formService.dtType.value;
      chart.colors.length = chart.series.length / 2;
      chart.legend.enabled = false;
      
      return chart?.getOption() || null;
    }

    return null;
  })

  ngAfterViewInit(): void {
    const source1$ = this.formService.form.valueChanges;

    this.caerusApiService.fetchConfig('fluctuation_position_top_v2').subscribe((res: ResultBody<{[key: string]: MetricsConfig[]}>) => {
      // console.log(res.data);
      
      if (res.data) {
        const finishMetrics = res.data['finishMetrics'].map(item => ({ ...item, diff: null, ratio: null, value: null }));
        this.finishMetrics.set(finishMetrics);
        this._finishMetrics = finishMetrics;

        const bookMetrics = res.data['bookMetrics'].map(item => ({ ...item, diff: null, ratio: null, value: null }));
        this.bookMetrics.set(bookMetrics);
        this._bookMetrics = bookMetrics;

        const finishLegends = res.data['finishLegends'].map(item => ({ ...item, checked: false }));
        this.finishLegends.set(finishLegends);

        const bookLegends = res.data['bookLegends'].map(item => ({ ...item, checked: false }));
        this.bookLegends.set(bookLegends);

        this.fetchMetrics();
      }
    })

    combineLatest([source1$, this.source2$, this.compareTotal$]).pipe(
      debounceTime(100),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(([, , compareTotal]) => {
      this.allChannel.set(null);
      this.otherChannel.set(null);

      if (compareTotal) {
        this.fetchAllChannel();
      }

      this.fetchOtherChannel();
    })

    this._subscribeToFormAndMetricsChange();
  }


  private _subscribeToFormAndMetricsChange() {
    combineLatest([this.formService.form.valueChanges, this.parent.view$]).pipe(
      startWith([
        this.formService.form.getRawValue(), 
        this.parent.view()
      ]),
      debounceTime(100),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(() => {
      this.fetchMetrics();
    })
  }


  fetchMetrics() {   
    const body: QueryInputVo = this.formService.form.getRawValue();

    body.metrics = this.parent.view() === 'finish' ? this._finishMetrics : this._bookMetrics;
    
    return this.apiService.search(body).subscribe(res => {
      if (res.data?.data) {
        const { data } = res.data;
        
        if (data.length > 0) {
          Object.keys(data[0]).forEach(key => {
            const value = data[0][key];

            this._updateMetrics(key, value);
          })
        } else {
          this.noData.set(true);
        }
      } else {
        this.errorMessage.set(res.error || res.message);
      }
    })
  }


  @SwitchMap()
  fetchAllChannel() {
    const body = this.formService.value();
    const metrics = this.selectedLegends();
    body.metrics = metrics;
    body.filter = { type: null, items: [] };

    trace('%c[对比大盘请求参数]', 'color: #ff0000', body);
    this.querying1.set(true);
    return this.apiService.search(body).pipe(
      finalize(() => this.querying1.set(false))
    ).subscribe(res => {
      const data = res.data;
      const property = '__name__';

      data.compareData = updateDataItems(data.compareData, property, () => '大盘');
      data.data = updateDataItems(data.data, property, () => '大盘');

      const dataGroup = groupBy(data.data, property);
      const compareDataGroup = groupBy(data.compareData, property);
      const { dt, compareDt, dtType } = this.formService.form.getRawValue();

      if (this.compareTotal()) {
        data.headers[property] = new QueryOutputHeaderVo();
        data.data = processDataGroup(dataGroup, data.headers, dt, dtType, property, () => `大盘`);
        data.compareData = processDataGroup(compareDataGroup, data.headers, compareDt, dtType, property, () => `大盘`);
      } else {
        data.data = processDataGroup(dataGroup, data.headers, dt, dtType);
        data.compareData = processDataGroup(compareDataGroup, data.headers, compareDt, dtType);
      }

      this.allChannel.set(data);
    });
  }


  @SwitchMap()
  fetchOtherChannel() {
    const body = this.formService.value();
    const metrics = this.selectedLegends();
    let area = '';
    body.metrics = metrics;
    
    if (this.root.areaForm$()?.extendName) {
      area = this.root.areaForm$().value[0].value;
    }

    // console.log('%c[请求参数]', 'color: #ff0000', body);
    this.querying2.set(true);
    return this.apiService.search(body).pipe(
      finalize(() => this.querying2.set(false))
    ).subscribe(res => {
      const data = res.data;
      const property = '__name__';

      if (data) {
        data.compareData = updateDataItems(data?.compareData || [], property, () => area);
        data.data = updateDataItems(data?.data || [], property, () => area);

        const dataGroup = groupBy(data.data, property);
        const compareDataGroup = groupBy(data.compareData, property);
        const { dt, compareDt, dtType } = this.formService.form.getRawValue();

        if (this.compareTotal()) {
          data.headers[property] = new QueryOutputHeaderVo();
          data.data = processDataGroup(dataGroup, data.headers, dt, dtType, property, () => area);
          data.compareData = processDataGroup(compareDataGroup, data.headers, compareDt, dtType, property, () => area);
        } else {
          data.data = processDataGroup(dataGroup, data.headers, dt, dtType);
          data.compareData = processDataGroup(compareDataGroup, data.headers, compareDt, dtType);
        }
        
        this.otherChannel.set(data);
      } else {
        console.warn('data:', data);
      }
    });
  }


  private _updateMetrics(key: string, value: string) {
    const target = this.parent.view() === 'finish' ? this.finishMetrics : this.bookMetrics;

    target.update(items => items.map(item => {
      if (`${item.extendName}_DIFF` === key) {
        item.diff = value;
      }
      if (`${item.extendName}_DIFF_RATIO` === key) {
        item.ratio = value;
      }
      if (item.extendName === key) {
        item.value = value;
      }
      return item;
    }))
  }

  
  onMetricsChange(metric) {
    if (!metric) { return; }
    
    if (this.parent.view() === 'finish') {
      this.finishLegends.update(items => {
        return items.map((item) => {
          return { ...item, checked: item.key === metric.key };
        });
      })
    }
    else {
      this.bookLegends.update(items => {
        return items.map((item) => {
          return { ...item, checked: item.key === metric.key };
        });
      })
    }

    this.onLegendChange();
  }


  scrollToAnchor() {
    try {
      const { top } = this.anchor().nativeElement.getBoundingClientRect()
      
      window.scrollTo({ top: window.scrollY + top, behavior: 'smooth' });
    } catch (error) {
      console.error('anchor is undefined');
    }
  }


  /**
   * 波动定位指标卡点击埋点
   * @param item 
   */
  handlerIndicatorCardClick(item: MetricsConfig) {
    // trace(`埋点上报: 波动定位指标卡点击 -> ${item.aliasName}`);
    this.buriedPointService.addStat('dida_dpm_caerus_indicator_card_click', {
      page_name: this.page_name,
      indicator_name: item.aliasName
    })
  }


  onLegendChange() {
    let checked = [];

    if (this.parent.view() === 'finish') {
      checked = this.finishLegends().filter(item => item.checked);
    } else {
      checked = this.bookLegends().filter(item => item.checked);
    }

    this.selectedLegends.set(checked);
  }

  
  onVisible(graph_position: number) {
    // trace('曝光埋点上报:页面内的图表（分区）', graph_position);
    this.buriedPointService.addStat('dida_dpm_caerus_indicator_graph_exposure', {
      page_name: this.page_name,
      graph_position
    });
  }
  
}
