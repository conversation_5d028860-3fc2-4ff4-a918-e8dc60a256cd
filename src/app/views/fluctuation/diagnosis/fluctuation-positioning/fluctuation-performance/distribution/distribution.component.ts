import { ChangeDetectionStrategy, ChangeDetectorRef, Component, DestroyRef, inject, linkedSignal } from '@angular/core';

import { TabsModule } from '@shared/modules/headless';
import { DistributionItemComponent } from './distribution-item/distribution-item.component';
import { FluctuationPerformanceComponent } from '../fluctuation-performance.component';


@Component({
  selector: 'app-distribution',
  template: `
    <app-tab-group class="relative block px-2 -translate-y-10 z-10" [selectedIndex]="index()">
      <div class="flex items-center">
        <span class="mr-auto font-black text-base">选择维度对比</span>
        <app-tab-list class="relative inline-flex items-center gap-x-0.5 p-0.5 bg-[#ebebec] rounded-lg">
          @for (item of dimensions(); track $index) {
            <app-tab class="dida-radio flex-none px-5 w-auto" activeClass="active">{{item.title}}</app-tab>
          }
        </app-tab-list>
      </div>


      <app-tab-panels>
        @for (item of dimensions(); track $index) {
          <app-tab-panel>
            <app-distribution-item [data]="item" />
          </app-tab-panel>
        }
      </app-tab-panels>
    </app-tab-group>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    TabsModule,
    DistributionItemComponent,
  ],
})
export class DistributionComponent {

  cdr = inject(ChangeDetectorRef);
  parent = inject(FluctuationPerformanceComponent);
  destroyRef = inject(DestroyRef);

  dimensions = linkedSignal<any, any[]>({
    source: () => this.parent.metric(),
    computation: (source) => {
      if (source) {
        const { bottomBarDimensionExtendNameList, barMetrics = [], ...item } = source;
        const { key: showName } = item;
        const metrics = [{
          ...item, 
          filter: { items: [], type: null },
          userDefAliasName: item.key,
          userDefExtendName: `COMPARE_${item.extendName}`,
          primary: true
        }];

        if (/DAU/.test(showName) === false) {
          return [
            {
              title: `里程对比`,
              showName,
              extendName: bottomBarDimensionExtendNameList[0],
              theme: ['#68bbc4', '#68bbc4'],
              metrics: metrics.concat(barMetrics), 
            },
            {
              title: `场景对比`,
              showName,
              extendName: bottomBarDimensionExtendNameList[1],
              theme: ['#fcca00', '#fcca00'],
              metrics, 
            },
          ];
        }
      }

      return [];
    }
  });

  index = linkedSignal({
    source: () => this.parent.metric(),
    computation: (source) => {
      const { key } = source;

      if (/DAU/.test(key)) {
        return -1;
      }

      return 0;
    }
  });

}
