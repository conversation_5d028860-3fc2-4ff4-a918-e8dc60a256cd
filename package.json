{"name": "bca-caerus-web", "version": "1.9.3", "scripts": {"ng": "ng", "start": "ng serve --no-hmr", "build": "ng build --configuration production && gzipper compress ./dist", "watch": "ng build --watch --configuration development", "extra": "npm install dida-web-common@2.1.73 --registry=http://mirror.didapinche.com/repository/npm-public"}, "private": true, "dependencies": {"@angular/animations": "19.2.0", "@angular/cdk": "19.2.1", "@angular/common": "19.2.0", "@angular/compiler": "19.2.0", "@angular/core": "19.2.0", "@angular/forms": "19.2.0", "@angular/platform-browser": "19.2.0", "@angular/platform-browser-dynamic": "19.2.0", "@angular/router": "19.2.0", "@bluehalo/ngx-leaflet": "^20.0.0", "@ctrl/tinycolor": "^4.1.0", "@types/leaflet": "^1.9.20", "@types/node": "^22.10.7", "echarts": "5.5.0", "gzipper": "7.2.0", "h3-js": "^4.2.1", "highcharts": "11.3.0", "highcharts-angular": "4.0.0", "leader-line": "^1.0.7", "leaflet": "^1.9.4", "lodash": "^4.17.21", "moment": "^2.30.1", "ng-zorro-antd": "19.2.2", "ngx-cookie-service": "19.1.2", "ngx-echarts": "19.0.0", "pinyin4js": "1.3.18", "reflect-metadata": "0.2.2", "rxjs": "7.8.0", "tslib": "2.8.1", "zone.js": "0.15.0"}, "devDependencies": {"@angular/build": "^19.2.4", "@angular/cli": "19.2.0", "@angular/compiler-cli": "19.2.0", "@tailwindcss/postcss": "^4.1.4", "autoprefixer": "10.4.17", "postcss": "8.5.3", "prettier": "3.5.3", "tailwindcss": "^4.1.4", "typescript": "5.5.4"}}