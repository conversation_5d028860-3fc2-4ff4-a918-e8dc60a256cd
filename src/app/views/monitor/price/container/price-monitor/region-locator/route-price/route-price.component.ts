import { JsonPipe } from '@angular/common'
import { DragDropModule } from '@angular/cdk/drag-drop'
import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, OnInit, signal } from '@angular/core'
import { ActivatedRoute } from '@angular/router'
import { FormsModule } from '@angular/forms'
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop'
import { combineLatest, debounceTime, finalize, startWith } from 'rxjs'
import { NzTableModule } from 'ng-zorro-antd/table'
// import { NzButtonComponent } from 'ng-zorro-antd/button'
import { NzOptionComponent, NzSelectComponent } from 'ng-zorro-antd/select'
import { NzToolTipModule } from 'ng-zorro-antd/tooltip'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
// import { NzTagComponent } from 'ng-zorro-antd/tag'
import { NzDropDownModule } from 'ng-zorro-antd/dropdown'
import { NzIconModule } from 'ng-zorro-antd/icon'
import { find } from 'lodash'

import { PAGE_NAME } from '@common/directive'
import { BuriedPointService } from '@common/service'
import { CaerusApiService } from '@api/caerus'
import { FilterItemVo } from '@api/query-engine/model'
import { DimensionValueMenuVo, MetricsMenuVo } from '@api/caerus/model'
import { isNotNull, sortBy } from '@common/function'
import { QueryEngineFormService } from '@common/service/query-engine'
import { SwitchMap } from '@common/decorator'
import { DropdownDirective } from '@shared/directives/dropdown'
import { RadioModule } from '@shared/modules/headless'
import { DebugDirective } from '@shared/directives'
import { GraphMiniColumnComponent } from '@shared/components/graph'
import { BuriedPointDirective } from '@shared/directives/buried-point'
import { ValueFormatter } from '@shared/components/value-formatter'
import { CityPickerComponent, SubFilterComponent } from '@views/monitor/price/components'
import { ReplacePipe } from '@views/monitor/price/pipes/replace'
import { PriceService } from '@views/monitor/price'
import { OnlinePricePerKmComponent } from './online-price-per-km/online-price-per-km.component'
import { PriceVarianceAvgComponent } from './price-variance-avg/price-variance-avg.component'
import { QueryEngineApiService } from '@api/query-engine'

@Component({
  selector: 'app-route-price',
  templateUrl: './route-price.component.html',
  styleUrl: './route-price.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class: 'relative block',
  },
  imports: [
    JsonPipe,
    DragDropModule,
    FormsModule,
    NzTableModule,
    NzDropDownModule,
    NzIconModule,
    NzToolTipModule,
    // NzButtonComponent,
    NzSelectComponent,
    NzOptionComponent,
    // NzTagComponent,
    NzPopoverModule,
    RadioModule,
    SubFilterComponent,
    GraphMiniColumnComponent,
    CityPickerComponent,
    OnlinePricePerKmComponent,
    PriceVarianceAvgComponent,
    ValueFormatter,
    DropdownDirective,
    BuriedPointDirective,
    DebugDirective,
    ReplacePipe,
  ],
})
export class RoutePriceComponent implements OnInit {
  page_name = inject(PAGE_NAME)
  destroyRef = inject(DestroyRef)
  apiService = inject(CaerusApiService)
  formService = inject(QueryEngineFormService)
  priceService = inject(PriceService)
  queryService = inject(QueryEngineApiService)

  cityRegion = signal<DimensionValueMenuVo[]>(null)
  priceType = signal<DimensionValueMenuVo>(null)
  orderType = signal<DimensionValueMenuVo>(null)
  miageIntal = signal<DimensionValueMenuVo>(null)
  #price_monitor_metrics = signal<MetricsMenuVo[]>(null)

  cityRegion$ = toObservable(this.cityRegion)
  priceType$ = toObservable(this.priceType)
  orderType$ = toObservable(this.orderType)
  miageIntal$ = toObservable(this.miageIntal)
  #price_monitor_metrics$ = toObservable(this.#price_monitor_metrics)

  priceTypeOptions = signal<DimensionValueMenuVo[]>([])
  orderTypeOptions = signal<DimensionValueMenuVo[]>([])
  interMiageOptions = signal<DimensionValueMenuVo[]>([])
  innerMiageOptions = signal<DimensionValueMenuVo[]>([])

  loading = signal(true)
  listOfData = signal([])
  city_regions = signal<DimensionValueMenuVo[]>([])

  setOfCheckedId = new Set<string>()
  setOfCheckedIds = signal([])

  merticsInfoMap = new Map<string, MetricsMenuVo>([])

  setOfCheckedIdsMaxLength = computed(() => {
    return this.setOfCheckedIds().length >= 2
  })

  showDiff = computed(() => {
    return !!this.formService.form$()?.compareDt
  })

  showMiniGraph = computed(() => {
    return this.miageIntal() === null
  })

  selected = computed(() => {
    return this.listOfData().filter(({ city_region: id }) => this.setOfCheckedIds().includes(id)) || []
  })

  listOfColumnsLevel_1 = computed(() => {
    return [
      {
        key: 'dida_conrtar_mean_expt_weight_samp_actprice_rate',
        colSpan: 3 - (this.showDiff() ? 0 : 1) - (this.showMiniGraph() ? 0 : 1),
        className: 'bg-[rgba(55,127,127,0.1)]!',
      },
      {
        key: 'dida_mean_samp_actprice_amt',
        colSpan: 4 - (this.showDiff() ? 0 : 1) - (this.showMiniGraph() ? 0 : 1),
        className: 'bg-[rgba(55,127,127,0.1)]!',
      },
      {
        key: 'driver_pass_active_prop',
        colSpan: 2 - (this.showDiff() ? 0 : 1),
        className: 'bg-[rgba(64,149,229,0.1)]!',
      },
      {
        key: 'c_active_book_pass_rate',
        colSpan: 2 - (this.showDiff() ? 0 : 1),
        className: 'bg-[rgba(64,149,229,0.1)]!',
      },
      {
        key: 'c_active_reply_driver_rate',
        colSpan: 2 - (this.showDiff() ? 0 : 1),
        className: 'bg-[rgba(64,149,229,0.1)]!',
        subTitle: '可参照基准值(23%)',
      },
      {
        key: 'c_book_finish_pass_rate',
        colSpan: 2 - (this.showDiff() ? 0 : 1),
        className: 'bg-[rgba(127,131,247,0.1)]!',
        subTitle: '可参照基准值(40%)',
      },
      {
        key: 'c_reply_finish_driver_rate',
        colSpan: 2 - (this.showDiff() ? 0 : 1),
        className: 'bg-[rgba(127,131,247,0.1)]!',
      },
      {
        key: 'c_own_active_finish_pass_rate',
        colSpan: 2 - (this.showDiff() ? 0 : 1),
        className: 'bg-[rgba(64,149,229,0.1)]!',
      },
      {
        key: 'c_active_finish_driver_rate',
        colSpan: 2 - (this.showDiff() ? 0 : 1),
        className: 'bg-[rgba(64,149,229,0.1)]!',
      },
    ]
  })

  listOfColumnsLevel_2 = computed<any[]>(() => {
    if (this.#price_monitor_metrics()) {
      return [
        [
          {
            name: '当前期值',
            title: `${this.merticsInfoMap.get('dida_conrtar_mean_expt_weight_samp_actprice_rate')?.aliasName}(当前期值)`,
            dataUnit: this.merticsInfoMap.get('dida_conrtar_mean_expt_weight_samp_actprice_rate')?.dataUnit,
            visible: true,
            className: 'bg-[rgba(55,127,127,0.1)]!',
            key: 'dida_conrtar_mean_expt_weight_samp_actprice_rate',
            sortFn: (a, b) =>
              a['dida_conrtar_mean_expt_weight_samp_actprice_rate'] -
              b['dida_conrtar_mean_expt_weight_samp_actprice_rate'],
            customFilter: true,
            filter: this.subFilter_1,
            suffix: '%',
          },
          {
            name: '各公里段数值',
            visible: this.showMiniGraph(),
            className: 'bg-[rgba(55,127,127,0.1)]!',
          },
          {
            name: '较对比期',
            title: `${this.merticsInfoMap.get('dida_conrtar_mean_expt_weight_samp_actprice_rate')?.aliasName}(较对比期)`,
            dataUnit: this.merticsInfoMap.get('dida_conrtar_mean_expt_weight_samp_actprice_rate')?.dataUnit,
            visible: this.showDiff(),
            className: 'bg-[rgba(55,127,127,0.1)]!',
            key: 'dida_conrtar_mean_samp_actprice_rate_DIFF',
            sortFn: (a, b) =>
              a['dida_conrtar_mean_samp_actprice_rate_DIFF'] - b['dida_conrtar_mean_samp_actprice_rate_DIFF'],
            customFilter: true,
            filter: this.subFilter_2,
            suffix: 'pp',
          },
        ], // 嘀嗒与DD线上实价差异率均值
        [
          {
            name: '当前期值',
            title: `${this.merticsInfoMap.get('dida_mean_samp_actprice_amt')?.aliasName}(当前期值)`,
            dataUnit: this.merticsInfoMap.get('dida_mean_samp_actprice_amt')?.dataUnit,
            visible: true,
            className: 'bg-[rgba(55,127,127,0.1)]!',
            key: 'dida_mean_samp_actprice_amt',
            sortFn: (a, b) => a['dida_mean_samp_actprice_amt'] - b['dida_mean_samp_actprice_amt'],
            customFilter: true,
            filter: this.subFilter_3,
          },
          {
            name: '各公里段数值',
            visible: this.showMiniGraph(),
            className: 'bg-[rgba(55,127,127,0.1)]!',
          },
          {
            name: '较基准期(24年3月份)',
            title: `${this.merticsInfoMap.get('dida_mean_samp_actprice_amt')?.aliasName}-较基准期(24年3月份)`,
            dataUnit: this.merticsInfoMap.get('A1')?.dataUnit,
            visible: true,
            className: 'bg-[rgba(55,127,127,0.1)]!',
            key: 'A1',
            sortFn: (a, b) => a['A1'] - b['A1'],
            customFilter: true,
            filter: this.subFilter_4,
          },
          {
            name: '较对比期',
            title: `${this.merticsInfoMap.get('dida_mean_samp_actprice_amt')?.aliasName}(较对比期)`,
            dataUnit: this.merticsInfoMap.get('dida_mean_samp_actprice_amt')?.dataUnit,
            visible: this.showDiff(),
            className: 'bg-[rgba(55,127,127,0.1)]!',
            key: 'dida_mean_samp_actprice_amt_DIFF',
            sortFn: (a, b) => a['dida_mean_samp_actprice_amt_DIFF'] - b['dida_mean_samp_actprice_amt_DIFF'],
            customFilter: true,
            filter: this.subFilter_5,
          },
        ], // 嘀嗒线上实价-每公里均价

        [
          {
            name: '当前期值',
            title: `${this.merticsInfoMap.get('driver_pass_active_prop')?.aliasName}(当前期值)`,
            dataUnit: this.merticsInfoMap.get('driver_pass_active_prop')?.dataUnit,
            visible: true,
            className: 'bg-[rgba(64,149,229,0.1)]!',
            key: 'driver_pass_active_prop',
            sortFn: (a, b) => a['driver_pass_active_prop'] - b['driver_pass_active_prop'],
            customFilter: true,
            filter: this.subFilter_6,
          },
          {
            name: '较对比期',
            title: `${this.merticsInfoMap.get('driver_pass_active_prop')?.aliasName}(较对比期)`,
            dataUnit: this.merticsInfoMap.get('driver_pass_active_prop')?.dataUnit,
            visible: this.showDiff(),
            className: 'bg-[rgba(64,149,229,0.1)]!',
            key: 'driver_pass_active_prop_DIFF',
            sortFn: (a, b) => a['driver_pass_active_prop_DIFF'] - b['driver_pass_active_prop_DIFF'],
            customFilter: true,
            filter: this.subFilter_7,
          },
        ], // 车乘比
        [
          {
            name: '当前期值',
            title: `${this.merticsInfoMap.get('c_active_book_pass_rate')?.aliasName}(当前期值)`,
            dataUnit: this.merticsInfoMap.get('c_active_book_pass_rate')?.dataUnit,
            visible: true,
            className: 'bg-[rgba(64,149,229,0.1)]!',
            key: 'c_active_book_pass_rate',
            sortFn: (a, b) => a['c_active_book_pass_rate'] - b['c_active_book_pass_rate'],
            customFilter: true,
            filter: this.subFilter_8,
            suffix: '%',
          },
          {
            name: '较对比期',
            title: `${this.merticsInfoMap.get('c_active_book_pass_rate')?.aliasName}(较对比期)`,
            dataUnit: this.merticsInfoMap.get('c_active_book_pass_rate')?.dataUnit,
            visible: this.showDiff(),
            className: 'bg-[rgba(64,149,229,0.1)]!',
            key: 'c_active_book_pass_rate_DIFF',
            sortFn: (a, b) => a['c_active_book_pass_rate_DIFF'] - b['c_active_book_pass_rate_DIFF'],
            customFilter: true,
            filter: this.subFilter_9,
            suffix: 'pp',
          },
        ], // 乘客下单率
        [
          {
            name: '当前期值',
            title: `${this.merticsInfoMap.get('c_active_reply_driver_rate')?.aliasName}(当前期值)`,
            dataUnit: this.merticsInfoMap.get('c_active_reply_driver_rate')?.dataUnit,
            visible: true,
            className: 'bg-[rgba(64,149,229,0.1)]!',
            key: 'c_active_reply_driver_rate',
            sortFn: (a, b) => a['c_active_reply_driver_rate'] - b['c_active_reply_driver_rate'],
            customFilter: true,
            filter: this.subFilter_10,
            suffix: '%',
          },
          // {
          //   name: '较基准值(23%)',
          //   title: `${this.merticsInfoMap.get('c_active_reply_driver_rate')?.aliasName}-较基准值(23%)`,
          //   dataUnit: this.merticsInfoMap.get('c_active_reply_driver_rate')?.dataUnit,
          //   visible: true,
          //   className: 'bg-[rgba(55,127,127,0.1)]!',
          //   key: 'A2',
          //   sortFn: (a, b) => a['A2'] - b['A2'],
          //   customFilter: true,
          //   filter: this.subFilter_11,
          // },
          {
            name: '较对比期',
            title: `${this.merticsInfoMap.get('c_active_reply_driver_rate')?.aliasName}(较对比期)`,
            dataUnit: this.merticsInfoMap.get('c_active_reply_driver_rate')?.dataUnit,
            visible: this.showDiff(),
            className: 'bg-[rgba(64,149,229,0.1)]!',
            key: 'c_active_reply_driver_rate_DIFF',
            sortFn: (a, b) => a['c_active_reply_driver_rate_DIFF'] - b['c_active_reply_driver_rate_DIFF'],
            customFilter: true,
            filter: this.subFilter_12,
            suffix: 'pp',
          },
        ], // 车主接单率

        [
          {
            name: '当前期值',
            title: `${this.merticsInfoMap.get('c_book_finish_pass_rate')?.aliasName}(当前期值)`,
            dataUnit: this.merticsInfoMap.get('c_book_finish_pass_rate')?.dataUnit,
            visible: true,
            className: 'bg-[rgba(127,131,247,0.1)]!',
            key: 'c_book_finish_pass_rate',
            sortFn: (a, b) => a['c_book_finish_pass_rate'] - b['c_book_finish_pass_rate'],
            customFilter: true,
            filter: this.subFilter_13,
            suffix: '%',
          },
          // {
          //   name: '较基准值(40%)',
          //   title: `${this.merticsInfoMap.get('dida_mean_samp_actprice_amt')?.aliasName}-较基准值(40%)`,
          //   dataUnit: this.merticsInfoMap.get('dida_mean_samp_actprice_amt')?.dataUnit,
          //   visible: true,
          //   className: 'bg-[rgba(55,127,127,0.1)]!',
          //   key: 'A3',
          //   sortFn: (a, b) => a['A3'] - b['A3'],
          //   customFilter: true,
          //   filter: this.subFilter_14,
          // },
          {
            name: '较对比期',
            title: `${this.merticsInfoMap.get('c_book_finish_pass_rate')?.aliasName}(较对比期)`,
            dataUnit: this.merticsInfoMap.get('c_book_finish_pass_rate')?.dataUnit,
            visible: this.showDiff(),
            className: 'bg-[rgba(127,131,247,0.1)]!',
            key: 'c_book_finish_pass_rate_DIFF',
            sortFn: (a, b) => a['c_book_finish_pass_rate_DIFF'] - b['c_book_finish_pass_rate_DIFF'],
            customFilter: true,
            filter: this.subFilter_15,
            suffix: 'pp',
          },
        ], // 下单完单率(乘客
        [
          {
            name: '当前期值',
            title: `${this.merticsInfoMap.get('c_reply_finish_driver_rate')?.aliasName}(当前期值)`,
            dataUnit: this.merticsInfoMap.get('c_reply_finish_driver_rate')?.dataUnit,
            visible: true,
            className: 'bg-[rgba(127,131,247,0.1)]!',
            key: 'c_reply_finish_driver_rate',
            sortFn: (a, b) => a['c_reply_finish_driver_rate'] - b['c_reply_finish_driver_rate'],
            customFilter: true,
            filter: this.subFilter_16,
            suffix: '%',
          },
          {
            name: '较对比期',
            title: `${this.merticsInfoMap.get('c_reply_finish_driver_rate')?.aliasName}(较对比期)`,
            dataUnit: this.merticsInfoMap.get('c_reply_finish_driver_rate')?.dataUnit,
            visible: this.showDiff(),
            className: 'bg-[rgba(127,131,247,0.1)]!',
            key: 'c_reply_finish_driver_rate_DIFF',
            sortFn: (a, b) => a['c_reply_finish_driver_rate_DIFF'] - b['c_reply_finish_driver_rate_DIFF'],
            customFilter: true,
            filter: this.subFilter_17,
            suffix: 'pp',
          },
        ], // 接单完单率(车主)

        [
          {
            name: '当前期值',
            title: `${this.merticsInfoMap.get('c_own_active_finish_pass_rate')?.aliasName}(当前期值)`,
            dataUnit: this.merticsInfoMap.get('c_own_active_finish_pass_rate')?.dataUnit,
            visible: true,
            className: 'bg-[rgba(64,149,229,0.1)]!',
            key: 'c_own_active_finish_pass_rate',
            sortFn: (a, b) => a['c_own_active_finish_pass_rate'] - b['c_own_active_finish_pass_rate'],
            customFilter: true,
            filter: this.subFilter_18,
            suffix: '%',
          },
          {
            name: '较对比期',
            title: `${this.merticsInfoMap.get('c_own_active_finish_pass_rate')?.aliasName}(较对比期)`,
            dataUnit: this.merticsInfoMap.get('c_own_active_finish_pass_rate')?.dataUnit,
            visible: this.showDiff(),
            className: 'bg-[rgba(64,149,229,0.1)]!',
            key: 'c_own_active_finish_pass_rate_DIFF',
            sortFn: (a, b) => a['c_own_active_finish_pass_rate_DIFF'] - b['c_own_active_finish_pass_rate_DIFF'],
            customFilter: true,
            filter: this.subFilter_19,
            suffix: 'pp',
          },
        ], // 自有乘客活跃完单率(UCVR)
        [
          {
            name: '当前期值',
            title: `${this.merticsInfoMap.get('c_active_finish_driver_rate')?.aliasName}(当前期值)`,
            dataUnit: this.merticsInfoMap.get('c_active_finish_driver_rate')?.dataUnit,
            visible: true,
            className: 'bg-[rgba(64,149,229,0.1)]!',
            key: 'c_active_finish_driver_rate',
            sortFn: (a, b) => a['c_active_finish_driver_rate'] - b['c_active_finish_driver_rate'],
            customFilter: true,
            filter: this.subFilter_20,
            suffix: '%',
          },
          {
            name: '较对比期',
            title: `${this.merticsInfoMap.get('c_active_finish_driver_rate')?.aliasName}(较对比期)`,
            dataUnit: this.merticsInfoMap.get('c_active_finish_driver_rate')?.dataUnit,
            visible: this.showDiff(),
            className: 'bg-[rgba(64,149,229,0.1)]!',
            key: 'c_active_finish_driver_rate_DIFF',
            sortFn: (a, b) => a['c_active_finish_driver_rate_DIFF'] - b['c_active_finish_driver_rate_DIFF'],
            customFilter: true,
            filter: this.subFilter_21,
            suffix: 'pp',
          },
        ], // 车主活跃完单率(UCVR)
      ].flat(1)
    }
    return []
  })

  scrollConfig = computed(() => {
    return !this.showDiff() && !this.showMiniGraph()
      ? null
      : !this.showDiff() && this.showMiniGraph()
        ? { x: '2200px' }
        : this.showDiff() && this.showMiniGraph()
          ? { x: '3000px' }
          : this.showDiff() && !this.showMiniGraph()
            ? { x: '2600px' }
            : null
  })

  subFilter_1 = signal<FilterItemVo>(null)
  subFilter_1$ = toObservable(this.subFilter_1)
  subFilter_2 = signal<FilterItemVo>(null)
  subFilter_2$ = toObservable(this.subFilter_2)
  subFilter_3 = signal<FilterItemVo>(null)
  subFilter_3$ = toObservable(this.subFilter_3)

  subFilter_4 = signal<FilterItemVo>(null)
  subFilter_4$ = toObservable(this.subFilter_4)
  subFilter_5 = signal<FilterItemVo>(null)
  subFilter_5$ = toObservable(this.subFilter_5)

  subFilter_6 = signal<FilterItemVo>(null)
  subFilter_6$ = toObservable(this.subFilter_6)
  subFilter_7 = signal<FilterItemVo>(null)
  subFilter_7$ = toObservable(this.subFilter_7)

  subFilter_8 = signal<FilterItemVo>(null)
  subFilter_8$ = toObservable(this.subFilter_8)
  subFilter_9 = signal<FilterItemVo>(null)
  subFilter_9$ = toObservable(this.subFilter_9)

  subFilter_10 = signal<FilterItemVo>(null)
  subFilter_10$ = toObservable(this.subFilter_10)
  subFilter_11 = signal<FilterItemVo>(null)
  subFilter_11$ = toObservable(this.subFilter_11)
  subFilter_12 = signal<FilterItemVo>(null)
  subFilter_12$ = toObservable(this.subFilter_12)

  subFilter_13 = signal<FilterItemVo>(null)
  subFilter_13$ = toObservable(this.subFilter_13)
  subFilter_14 = signal<FilterItemVo>(null)
  subFilter_14$ = toObservable(this.subFilter_14)
  subFilter_15 = signal<FilterItemVo>(null)
  subFilter_15$ = toObservable(this.subFilter_15)

  subFilter_16 = signal<FilterItemVo>(null)
  subFilter_16$ = toObservable(this.subFilter_16)
  subFilter_17 = signal<FilterItemVo>(null)
  subFilter_17$ = toObservable(this.subFilter_17)

  subFilter_18 = signal<FilterItemVo>(null)
  subFilter_18$ = toObservable(this.subFilter_18)
  subFilter_19 = signal<FilterItemVo>(null)
  subFilter_19$ = toObservable(this.subFilter_19)

  subFilter_20 = signal<FilterItemVo>(null)
  subFilter_20$ = toObservable(this.subFilter_20)
  subFilter_21 = signal<FilterItemVo>(null)
  subFilter_21$ = toObservable(this.subFilter_21)

  params = signal<any>(null)
  readonly buriedPointService = inject(BuriedPointService)

  constructor(private route: ActivatedRoute) {
    this.route.queryParamMap.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(paramMap => {
      this.params.set(paramMap['params'])
    })
  }

  ngOnInit(): void {
    this._fetchMetricsConfig()
    this._fetchDimensionConfig()
    this._subscribeToOrderTypeChange()
    this._subscribeToFilterItemChange()
  }

  private _subscribeToFilterItemChange() {
    combineLatest([
      this.#price_monitor_metrics$,
      this.priceService.competitionFilter$,
      this.cityRegion$,
      this.priceType$,
      this.orderType$,
      this.miageIntal$,
      this.subFilter_1$,
      this.subFilter_2$,
      this.subFilter_3$,
      this.subFilter_4$,
      this.subFilter_5$,
      this.subFilter_6$,
      this.subFilter_7$,
      this.subFilter_8$,
      this.subFilter_9$,
      this.subFilter_10$,
      this.subFilter_11$,
      this.subFilter_12$,
      this.subFilter_13$,
      this.subFilter_14$,
      this.subFilter_15$,
      this.subFilter_16$,
      this.subFilter_17$,
      this.subFilter_18$,
      this.subFilter_19$,
      this.subFilter_20$,
      this.subFilter_21$,
      this.formService.form.valueChanges.pipe(startWith(this.formService.form.value)),
    ])
      .pipe(startWith([]), debounceTime(100), takeUntilDestroyed(this.destroyRef))
      .subscribe(([metrics]) => {
        if (isNotNull(metrics)) {
          this.query()
        }
      })
  }

  private _subscribeToOrderTypeChange() {
    this.orderType$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      this.miageIntal.set(null)
    })
  }

  private _fetchMetricsConfig() {
    this.apiService.fetchMetricsConfig('price_monitor_metrics_v3').subscribe(res => {
      if (res.status !== '00000') {
        return console.error(res.message || res.error)
      }

      const { subMetric } = res.data['table_route_price']
      // const price_monitor = res.data['price_monitor'];

      // console.log(subMetric, price_monitor);
      subMetric.forEach(item => {
        this.merticsInfoMap.set(item.extendName, item)
      })

      this.#price_monitor_metrics.set(null)

      setTimeout(() => {
        this.#price_monitor_metrics.set(subMetric)
      }, 100)
    })
  }

  private _fetchDimensionConfig() {
    this.apiService.fetchDimensionConfig('price_monitor_dimension').subscribe(res => {
      if (res.status !== '00000') {
        return console.error(res.message || res.error)
      }
      // console.log('fetchDimensionConfig', res.data)

      const price_type = res.data.price_save.find(item => item.keyName === 'price_type')
      const c_ord_type = res.data.price_save.find(item => item.keyName === 'c_ord_type')
      const inter_miage_intal = res.data.price_save.find(item => item.keyName === 'inter_miage_intal')
      const inner_miage_intal = res.data.price_save.find(item => item.keyName === 'inner_miage_intal')
      const city_regions = res.data.price_save.find(item => item.keyName === 'price_city_region')

      this.priceTypeOptions.set(price_type.values.sort(sortBy(['拼成', '未拼成', '独享'], 'showValue')))
      this.orderTypeOptions.set(c_ord_type.values.sort(sortBy(['市内', '城际'], 'showValue')))
      this.interMiageOptions.set(inter_miage_intal.values)
      this.innerMiageOptions.set(inner_miage_intal.values)

      this.city_regions.set(city_regions.values)
      // console.log('param===', this.params())
      const {
        priceType: paramsPriceType,
        orderType: paramsOrderType,
        cityRegion: paramsCityRegion,
        regionType: paramsRegionType,
      } = this.params()
      const _cityRegion = this._getRegionsFromRouteParams()

      this.priceType.set(find(this.priceTypeOptions(), ['key', paramsPriceType]) || this.priceTypeOptions()[1])
      this.orderType.set(find(this.orderTypeOptions(), ['key', paramsOrderType]) || this.orderTypeOptions()[0])
      this.cityRegion.set(_cityRegion || null)
    })
  }

  private _getRegionsFromRouteParams() {
    const { cityRegion, regionType } = this.params()

    switch (regionType) {
      case 'province_name':
        return this.city_regions().filter(item => item.relValue === cityRegion)
      default:
        return this.city_regions().filter(item => item.value === cityRegion)
    }
  }

  private _cityRegionFilter() {
    if (this.cityRegion() && this.cityRegion().length > 0) {
      return [
        {
          conditionType: 2,
          condition: 'in',
          id: null,
          valueType: null,
          extendName: 'city_region',
          value: this.cityRegion(),
        },
      ]
    }
    return []
  }

  private _channelFilter() {
    return [
      {
        conditionType: 2,
        condition: '=',
        id: null,
        valueType: null,
        extendName: 'c_firlev_channel',
        value: [
          {
            key: '自有渠道',
            value: '自有渠道',
          },
        ],
      },
    ]
  }

  private _priceTypeFilter() {
    if (this.priceType()) {
      return [
        {
          conditionType: 2,
          condition: 'in',
          id: null,
          extendName: 'price_type',
          value: [this.priceType()],
          valueType: null,
        },
      ]
    }
    return []
  }

  private _orderTypeFilter() {
    if (this.orderType()) {
      return [
        {
          conditionType: 2,
          condition: '=',
          id: null,
          extendName: 'c_ord_type',
          value: [this.orderType()],
          valueType: null,
        },
      ]
    }
    return []
  }

  download() {
    this.queryService.exportToExcel(this._value()).subscribe(() => {})
  }

  private _miageIntalFilter() {
    const extendName = this.orderType()?.showValue === '城际' ? 'inter_miage_intal' : 'inner_miage_intal'

    if (this.miageIntal()) {
      return [
        {
          conditionType: 2,
          condition: '=',
          id: null,
          extendName,
          value: [this.miageIntal()],
          valueType: null,
        },
      ]
    }
    return []
  }

  private _value() {
    const body = this.formService.value()

    body.dimensions = [{ extendName: 'city_region' }]
    body.metrics = this.#price_monitor_metrics().map(
      ({ bizExpression, aliasName, dataUnit, display, tagOrder, type, displayOrder, recommend, ...item }) => {
        const items =
          item.tagName === 'price'
            ? [this._priceTypeFilter(), this._orderTypeFilter(), this._miageIntalFilter()]
            : item.tagName === 'order'
              ? [this._channelFilter()]
              : item.tagName === 'dau'
                ? [this._channelFilter(), this._orderTypeFilter(), this._miageIntalFilter()]
                : []

        if (item.extendName === 'dida_conrtar_mean_expt_weight_samp_actprice_rate') {
          items.push(this.priceService.competitionFilter())
        }

        return {
          ...item,
          userDefExtendName: item.extendName,
          filter: {
            items: items.flat(1),
          },
        }
      }
    )

    body.metrics = body.metrics.concat([
      {
        extendName: 'city_price_top_finish_ord_weight',
        userDefExtendName: 'city_price_top_finish_ord_weight',
        filter: {
          items: [],
        },
      },
      {
        extendName: 'c_finish_ord_cnt',
        userDefExtendName: 'c_finish_ord_cnt',
        filter: {
          items: [
            {
              conditionType: 2,
              condition: '=',
              extendName: 'c_firlev_channel',
              value: [{ key: '自有渠道', value: '自有渠道' }],
            },
          ],
        },
      },
    ])

    body.metricsFilter = {
      items: [
        this.subFilter_1(),
        this.subFilter_2(),
        this.subFilter_3(),
        this.subFilter_4(),
        this.subFilter_5(),
        this.subFilter_6(),
        this.subFilter_7(),
        this.subFilter_8(),
        this.subFilter_9(),
        this.subFilter_10(),
        this.subFilter_11(),
        this.subFilter_12(),
        this.subFilter_13(),
        this.subFilter_14(),
        this.subFilter_15(),
        this.subFilter_16(),
        this.subFilter_17(),
        this.subFilter_18(),
        this.subFilter_19(),
        this.subFilter_20(),
        this.subFilter_21(),
      ].filter(isNotNull),
    }

    if (this.priceService.competitionName() === 'GD') {
      body.metricsFilter.items.push({
        conditionType: 2,
        condition: '!=',
        valueType: 'metrics',
        extendName: 'dida_conrtar_mean_expt_weight_samp_actprice_rate',
        value: [
          {
            key: 'null',
            value: 'null',
          },
        ],
      })
    }

    body.metricsFilter.items = body.metricsFilter.items.filter(isNotNull)

    body.orders = [
      {
        extendName: 'city_price_top_finish_ord_weight',
        type: 'asc',
      },
      {
        extendName: 'c_finish_ord_cnt',
        type: 'desc',
      },
    ]

    body.filter.items = [...this._cityRegionFilter()]

    body.dataFillConfig = {
      open: 1,
      fillRangeType: 2,
      fillValue: 0,
    }

    body.scene = 7

    body.userDefExpressions = [
      {
        userDefAliasName: '嘀嗒线上价格-每公里均价（较基期）',
        userDefExtendName: 'A1',
        expression: 'dida_mean_samp_actprice_amt-dida_benchmark_book_ord_amt',
      },
      {
        userDefAliasName: '车主活跃接单率（较基期）',
        userDefExtendName: 'A2',
        expression: 'c_reply_finish_driver_rate-0.23',
      },
      {
        userDefAliasName: '乘客下单完单率（较基期）',
        userDefExtendName: 'A3',
        expression: 'c_book_finish_pass_rate-0.4',
      },
      {
        userDefAliasName: '嘀嗒线上价格-每公里均价（较基期）',
        userDefExtendName: 'A4',
        expression: '(dida_mean_samp_actprice_amt-dida_benchmark_book_ord_amt)/dida_benchmark_book_ord_amt',
      },
    ]

    return body
  }

  @SwitchMap()
  private query() {
    const body = this._value()

    this.loading.set(true)
    return this.apiService
      .search(body, 'route-price-table')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        if (res.status !== '00000') {
          return console.error(res.error || res.message)
        }

        this.listOfData.set(res.data?.data || [])
        if (this.params() && Object.keys(this.params()).length !== 0) {
          this.quickCompare(this.cityRegion())
        }
      })
  }

  updateCheckedSet(id: string, checked: boolean): void {
    if (checked) {
      this.setOfCheckedId.add(id)
      this.setOfCheckedIds.update(ids => [...ids, id])
    } else {
      this.setOfCheckedId.delete(id)
      this.setOfCheckedIds.update(ids => ids.filter(item => item !== id))
    }
  }

  onItemChecked(city_region: string, checked: boolean): void {
    this.updateCheckedSet(city_region, checked)
  }

  quickCompare(cityRegion = null, flag = false) {
    let cityRegions
    console.log('this.city_regions()', this.city_regions())
    if (!cityRegion) {
      const selectedOfCity = this.selected().map(item => item.city_region)
      cityRegions = this.city_regions().filter(item => selectedOfCity.includes(item.value))
    } else if (flag) {
      cityRegions = this.city_regions().filter(item => cityRegion.includes(item.value))
    } else {
      cityRegions = cityRegion
    }

    const orderType = this.orderType()
    const priceType = this.priceType()
    const miageIntal = this.miageIntal()

    this.priceService.quickCompare({
      cityRegions,
      orderType,
      priceType,
      miageIntal,
      scroll: true,
    })
    this.buriedPointService.addStat('dida_dpm_caerus_Price_Contrast_click', {
      page_name: this.page_name,
      Pricetab_name: '路线价格',
    })
  }

  handleLinkClick(miageIntal, cityRegion: string) {
    const orderType = this.orderType()
    const priceType = this.priceType()
    this.buriedPointService.addStat('dida_dpm_caerus_Price_Resolution_click', {
      page_name: this.page_name,
    })
    this.priceService.priceDiff({
      cityName: cityRegion,
      orderType,
      priceType,
      miageIntal,
    })
  }
}
