import { DatePipe } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AfterViewInit, ChangeDetectionStrategy, Component, DestroyRef, booleanAttribute, computed, effect, inject, input, numberAttribute, signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { addDays, addWeeks, addMonths, addYears, subYears, subWeeks, subMonths, format, formatISO, startOfYear } from 'date-fns';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { startWith } from 'rxjs';

import { IconDeleteComponent } from '@shared/modules/icons';
import { QueryEngineFormService } from '@common/service/query-engine';
import { DiffTime, _pad, getFirstDayForWeek, isNotNull, isNotUndefined } from '@common/function';
import { WeekPickerComponent } from './week-picker/week-picker.component';
import { MonthPickerComponent } from './month-picker/month-picker.component';
import { DatePickerComponent } from './date-picker/date-picker.component';


@Component({
  selector: 'app-date-compare',
  templateUrl: './date-compare.ng.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class: 'px-2'
  },
  imports: [
    FormsModule,
    ReactiveFormsModule,
    NzSelectModule,
    NzButtonModule,
    NzDatePickerModule,
    IconDeleteComponent,
    WeekPickerComponent,
    MonthPickerComponent,
    DatePickerComponent,
  ],
  providers: [
    DatePipe,
  ]
})
export class DateCompareComponent implements AfterViewInit {

  compareBtn = input(false, { transform: booleanAttribute });
  deleteBtn = input(false, { transform: booleanAttribute });
  showDtType = input(true, { transform: booleanAttribute });
  gapX = input(1, { alias: 'gap-x', transform: numberAttribute });
  dateLabel = input('日期');
  dateLabelClass = input<string>(null);

  readonly destroyRef = inject(DestroyRef);
  readonly formService = inject(QueryEngineFormService);
  readonly datePipe = inject(DatePipe);

  // startTime = signal<any>(null);
  // endTime = signal<Date | any>(null);

  compareVisible = signal(false);
  dtType = toSignal(this.formService.form.get('dtType').valueChanges.pipe(
    startWith(this.formService.form.get('dtType').value)
  ));

  unitOptions = signal([
    { label: '按单日', value: 'dt' },
    { label: '按周', value: 'yw' },
    { label: '按月', value: 'ym' },
  ]);

  compareRangeMap = computed(() => {
    const now = new Date().setHours(0, 0, 0, 0);
    const startDateForMonth = new Date(now).setDate(1);
    const startDateForYear  = new Date(startDateForMonth).setMonth(0);
    const { startTime, endTime } = this.formService.form$().dt;
    const result = new Map();

    result.set('上周',   [getFirstDayForWeek(addWeeks(now, -1)), addDays(getFirstDayForWeek(addWeeks(now, -1)), 6)]);
    result.set('上月',   [addMonths(startDateForMonth, -1),      addDays(addMonths(startDateForMonth, 0), -1)]);
    result.set('去年',   [addYears(startDateForYear, -1),        addDays(addYears(startDateForYear, 0), -1)]);

    if (isNotNull(startTime, endTime)) {
      result.set('周同比', [addDays(new Date(startTime), -7),      addDays(new Date(endTime), -7)]);
      result.set('年同比', [addYears(new Date(startTime), -1),     addYears(new Date(endTime), -1)]);
    }

    return result;
  })

  rangeMap = () => {
    const now = new Date().setHours(0, 0, 0, 0);
    const startDateForMonth = new Date(now).setDate(1);
    const startDateForYear  = new Date(startDateForMonth).setMonth(0);
    const today = new Date().getDay();
    const result = new Map();

    // if (today !== 1) {
    //   result.set('本周', [getFirstDayForWeek(addWeeks(now, 0)), addDays(now, -1)]);
    // }

    // result.set('上周',    [getFirstDayForWeek(addWeeks(now, -1)), addDays(getFirstDayForWeek(addWeeks(now, -1)), 6)]);
    result.set('本月',    [addMonths(startDateForMonth, 0),       addDays(now, -1)]);
    result.set('上月',    [addMonths(startDateForMonth, -1),      addDays(addMonths(startDateForMonth, 0), -1)]);
    result.set('今年',    [addYears(startDateForYear, 0),         addDays(now, -1)]);
    result.set('近2年',   [addYears(startDateForYear, -1),        addDays(now, -1)]);
    result.set('近30天',  [addDays(now, -30),                     addDays(now, -1)]);
    result.set('近90天',  [addDays(now, -90),                     addDays(now, -1)]);
    result.set('近365天', [addDays(now, -365),                    addDays(now, -1)]);
    result.set('近730天', [addDays(now, -730),                    addDays(now, -1)]);

    return result;
  };


  weekRangeMap = () => {
    const result = new Map();
    const today = new Date().setHours(0, 0, 0, 0)
    const yesterday = addDays(today, -1);
    const startDateForMonth = new Date(today).setDate(1);
    const startDateForYear  = new Date(startDateForMonth).setMonth(0);

    result.set('近2周',   [subWeeks(today, 2),            addWeeks(yesterday, 0)]);
    result.set('近4周',   [subWeeks(today, 4),            addWeeks(yesterday, 0)]);
    result.set('近8周',   [subWeeks(today, 8),            addWeeks(yesterday, 0)]);
    result.set('近16周',  [subWeeks(today, 16),           addWeeks(yesterday, 0)]);
    result.set('近52周',  [subWeeks(today, 52),           addWeeks(yesterday, 0)]);
    result.set('近104周', [subWeeks(today, 104),          addWeeks(yesterday, 0)]);
    result.set('今年',    [addYears(startDateForYear, 0), addWeeks(yesterday, 0)]);
    result.set('近2年',   [subYears(startDateForYear, 2), addWeeks(yesterday, 0)]);

    return result;
  }


  monthRangeMap = () => {
    const result = new Map();
    const today = new Date();
    const startDateForMonth = new Date(today).setDate(1);
    const startDateForYear  = new Date(startDateForMonth).setMonth(0);

    result.set('过去3月',  [subMonths(today, 3), subMonths(today, 1)]);
    result.set('过去6月',  [subMonths(today, 6), subMonths(today, 1)]);
    result.set('过去12月', [subMonths(today, 12), subMonths(today, 1)]);
    result.set('过去24月', [subMonths(today, 24), subMonths(today, 1)]);
    result.set('今年',     [addYears(startDateForYear, 0), addDays(today, -1)]);
    result.set('近2年',    [addYears(startDateForYear, -1), addDays(today, -1)]);

    return result;
  }

  constructor() {
    effect(() => {
      const { compareDt } = this.formService.form$() || {};
      const visible = isNotUndefined(compareDt);

      this.compareVisible.set(visible);
    });
  }


  ngAfterViewInit(): void {
    this.formService.form.get('dtType').valueChanges.pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(() => {
      this.formService.form.get('dt.startTime').patchValue(null);
      this.formService.form.get('dt.endTime').patchValue(null);
      this.formService.form.get('compareDt')?.get('startTime')?.patchValue(null);
      this.formService.form.get('compareDt')?.get('endTime')?.patchValue(null);
    })
  }


  addDateCompare() {
    const { dt: {startTime, endTime}, dtType } = this.formService.form.getRawValue();

    this.formService.addDateCompare();

    if (startTime && endTime && dtType === 'dt') {
      const startDate = new Date(startTime);
      const endDate   = new Date(endTime);
      const dayCount  = new DiffTime(startDate, endDate).count('days');
      const compareDtStartTime = this.datePipe.transform(addDays(startDate, -(dayCount) + -1), 'yyyy-MM-dd');
      const compareDtEndTime   = this.datePipe.transform(addDays(startDate, -1), 'yyyy-MM-dd');

      // this.endTime.set([compareDtStartTime, compareDtEndTime]);
      this.formService.form.get('compareDt.startTime').patchValue(compareDtStartTime);
      this.formService.form.get('compareDt.endTime').patchValue(compareDtEndTime);
    }
  }


  removeDateCompare() {
    this.formService.removeDateCompare();
  }

}
