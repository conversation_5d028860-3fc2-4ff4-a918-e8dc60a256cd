import { AsyncPipe, DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, effect, inject, signal, ViewContainerRef } from '@angular/core';
import { rxResource } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { NzSpinComponent } from 'ng-zorro-antd/spin';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { map, of, tap } from 'rxjs';

import { trace } from '@common/const';
import { BuriedPointService } from '@common/service';
import { CaerusApiService } from '@api/caerus';
import { FilterItemVo, MetricsMenuVo } from '@api/caerus/model';
import { QueryEngineApiService } from '@api/query-engine';
import { QueryInputVo } from '@api/query-engine/model';
import { groupBy, isEmpty, isNotNull, isNull, toFilterItem } from '@common/function';
import { ModalService } from '@core/dialog';
import { IncrementPipe } from '@shared/pipes/increment';
import { ReplaceByPipe } from '@shared/pipes/replace-by';
import { DimensionFilterComponent } from '@shared/components/filters';
import { IndicatorModule } from '@shared/components/indicator';
import { RadioModule } from '@shared/modules/headless';
import { IconExpandComponent } from '@shared/modules/icons';
import { HomeComponent, HomeService } from '@views/home';
import { MetricOverviewComponent } from '../metric-overview.component';
import { MetricsDetailComponent } from '../metrics-detail';


@Component({
  selector: 'app-passenger',
  templateUrl: './passenger.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    AsyncPipe,
    FormsModule,
    NzSpinComponent,
    NzPopoverModule,
    IndicatorModule,
    RadioModule,
    DimensionFilterComponent,
    IconExpandComponent,
    ReplaceByPipe,
    IncrementPipe,
  ],
  providers: [
    DatePipe,
  ],
})
export class PassengerComponent {

  readonly datePipe = inject(DatePipe);
  readonly cdr = inject(ChangeDetectorRef);
  readonly viewContainerRef = inject(ViewContainerRef);
  readonly modalService = inject(ModalService);
  readonly service = inject(HomeService);
  readonly caerusApiService = inject(CaerusApiService);
  readonly apiService = inject(QueryEngineApiService);
  readonly buriedPointService = inject(BuriedPointService);
  readonly parent = inject(MetricOverviewComponent);
  readonly root = inject(HomeComponent);


  valueMap = new Map();
  metricsValueMap = signal(null);
  c_pass_fourca_user_type = signal<FilterItemVo>(null);

  dimensionConfigResource = rxResource({
    loader: () => this.caerusApiService.fetchDimensionConfig('cockpit_dim_v2').pipe(
      map(res => res.data.top_filter_left),
      // tap(res => console.log(res))
    )
  });

  fourcaUserTypeOptions = computed(() => {
    return this.dimensionConfigResource.value()
      ?.find(item => item.keyName === 'c_pass_fourca_user_type')
      ?.values
      ?.map(toFilterItem) || [];
  })
  
  metricsConfigResource = rxResource({
    loader: () => this.caerusApiService.fetchMetricsConfigV2('cockpit_top_metrics_v3').pipe(
      map(res => res.data['core_overview_pop2']?.subMetric),
      // tap(res => console.log(res))
    )
  });
  
  metricsDataResource = rxResource({
    request: () => this.metricsDataBody(),
    loader: ({ request }) => isEmpty(request.metrics) 
      ? of(null)
      : this.caerusApiService.getKeyProcessPassenger(request, 'process-passenger').pipe(
        tap(res => {
          if (res.status !== '00000') {
            throw res.message;
          }
        }),
        map(res => res.data?.data?.at(0)),
        // tap(value => {
        //   Object.keys(value).forEach(key => {
        //     if (key !== 'ym' && key.indexOf(':') === -1) {
        //       const target = this.valueMap.get(key);
        //       this.valueMap.set(key, { ...target, value: parseFloat(value[key]) || null });
        //     }
        //     else if (key.indexOf(':') > -1) {
        //       const [k, v] = key.split(':');
        //       const target = this.valueMap.get(k);

        //       this.valueMap.set(k, {  ...target, [v]: parseFloat(value[key]) || null });
        //     }
        //   })

        //   this.metricsValueMap.set(null);
        //   setTimeout(() => {
        //     this.metricsValueMap.set(this.valueMap);
        //     this.cdr.markForCheck();
        //   })
        // })
      )
  })

  specialMetricsDataResource = rxResource({
    request: () => this.specialMetricsDataBody(),
    loader: ({ request }) => (isNull(request) || isEmpty(request?.metrics))
      ? of(null)
      : this.caerusApiService.getKeyProcessPassengerNew(request, 'special-process-passenger').pipe(
        tap(res => {
          if (res.status !== '00000') {
            throw res.message;
          }
        }),
        map(res => res.data?.data?.at(0)),
      )
  })

  metricsDataBody = computed(() => {
    const month = this.datePipe.transform(this.service.month(), 'yyyy-MM');
    const dt = { startTime: month, endTime: month };
    const dimensions = [{ id: null, extendName: 'ym', predefineCompareType: ['ym', 'year'] }];
    const metrics = this.#metrics()?.map(item => {
      return {
        ...item,
        filter: this.#metric_filters(),
      };
    });

    const body = {
      dt,
      dtType: 'ym',
      metrics,
      dimensions,
      filter: this.#filters(),
      scene: 4,
      dataFillConfig: { open: 1, fillRangeType: 1, fillValue: 1 },
      queryType: 'compare',
      accelerateFilter: true,
    } as QueryInputVo;

    return body;
  })

  specialMetricsDataBody = computed(() => {
    const month = this.datePipe.transform(this.service.updateTime(), 'yyyy-MM-dd');
    const dt = { startTime: month, endTime: month };
    const dimensions = [{ id: null, extendName: 'dt', predefineCompareType: ['ym', 'year'] }];
    const metrics = this.#special_metrics()?.map(item => {
      return {
        ...item,
        filter: this.#metric_filters(),
      };
    });

    const body = {
      dt,
      dtType: 'dt',
      metrics,
      dimensions,
      filter: this.#filters(),
      scene: 4,
      dataFillConfig: { open: 1, fillRangeType: 1, fillValue: 1 },
      queryType: 'compare',
      accelerateFilter: true,
    } as QueryInputVo;

    if (!month) {
      return null;
    }

    return body;
  })

  #metrics = computed(() => {    
    return this.metricsConfigResource.value()
      ?.filter(item => !item?.dtQuery)
      ?.map(({ extendName, showName, aliasName }) => ({ 
        extendName, showName, 
        userDefExtendName: 'COMPARE_'+extendName, 
        userDefAliasName: aliasName,
      }));
  });

  #special_metrics = computed(() => {    
    return this.metricsConfigResource.value()
      ?.filter(item => item?.dtQuery === true)
      ?.map(({ extendName, showName, aliasName }) => ({ 
        extendName, showName, 
        userDefExtendName: 'COMPARE_'+extendName, 
        userDefAliasName: aliasName,
      }));
  });

  metricsList = computed(() => {
    if (this.metricsConfigResource.value()) {
      const res = this.metricsConfigResource.value().filter(item => item.display === 1);
      const groupObj = groupBy(res, 'tagName');
      const groupMap = new Map();
      const arr = Object.keys(groupObj);
      
      arr.forEach(key => {
        const item = groupObj[key];
        const { card } = item.at(0);

        if (groupMap.has(card)) {
          groupMap.set(card, groupMap.get(card).concat([item]));
        } else {
          groupMap.set(card, [item]);
        }
      })

      const items = [...groupMap.values()];

      // console.log('[items]', items);
      
      return items;
    }
    return [];
  })

  #metric_filters = computed(() => {
    const items = [
      this.parent.c_seclev_channel(),
    ].filter(isNotNull);

    return {
      items, 
      type: null
    }
  })

  #filters = computed(() => {
    const items = [
      this.parent.c_ord_type(),
      this.parent.c_firlev_channel(),
      this._globalAreaFilter(),
      this.c_pass_fourca_user_type(),
    ].filter(isNotNull);

    return { 
      items, 
      type: null
    }
  })

  constructor() {
    effect(() => {
      if (
        this.metricsDataResource.value() ||
        this.specialMetricsDataResource.value()
      ) {
        const value = {
          ...this.metricsDataResource.value() || {},
          ...this.specialMetricsDataResource.value() || {},
        };

        // debugger
        this.valueMap.clear();
        Object.keys(value).forEach(key => {
          if (key !== 'ym' && key.indexOf(':') === -1) {
            const target = this.valueMap.get(key);
            this.valueMap.set(key, { ...target, value: parseFloat(value[key]) || null });
          }
          else if (key.indexOf(':') > -1) {
            const [k, v] = key.split(':');
            const target = this.valueMap.get(k);

            this.valueMap.set(k, {  ...target, [v]: parseFloat(value[key]) || null });
          }
        })

        this.metricsValueMap.set(null);
        setTimeout(() => {
          this.metricsValueMap.set(this.valueMap);
          this.cdr.markForCheck();
        })
      }
    })

    effect(() => {
      trace(`埋点上报：模块筛选项点击`, {
        page_name: this.root.page_name,
        c_pass_fourca_user_type: this.c_pass_fourca_user_type()?.value?.at(0)?.value || '全部',
      });

      this.buriedPointService.addStat('dida_dpm_caerus_home_vip_fliter_click', {
        page_name: this.root.page_name,
        c_pass_fourca_user_type: this.c_pass_fourca_user_type()?.value?.at(0)?.value || '全部',
      });
    })
  }

  private _globalAreaFilter() {
    if (this.parent.area()) {
      const { key, value, extendName } = this.parent.area();
      
      return {
        conditionType: 2,
        condition: "=",
        id: null,
        extendName,
        value: [{ key, value }],
        valueType: null
      };
    }
    return null;
  }

  replaceFn = (text: string) => {
    if (text) {
      return text.replace(/乘客/, '');
    }
    return text;
  }

  openMetricsDetail() {
    console.clear();
    trace(`埋点上报：大盘核心指标总览-卡片右上角按钮点击`, {
      page_name: this.root.page_name,
      indicator_name: this.metricsList().at(0).at(0).at(0).extendName,
    });

    this.buriedPointService.addStat('dida_dpm_caerus_home_card_enlarge_click', {
      page_name: this.root.page_name,
      indicator_name: this.metricsList().at(0).at(0).at(0).extendName,
    });

    this.modalService.open(MetricsDetailComponent, { 
      viewContainerRef: this.viewContainerRef,
      data: {
        metrics: [this.metricsList().at(0).at(0).at(0)],
        filters: {
          area: this.parent.area()?.value,
          c_firlev_channel: this.parent.c_firlev_channel(),
          c_seclev_channel: this.parent.c_seclev_channel(),
          c_ord_type: this.parent.c_ord_type(),
        }
      }
    });
  }

}
