import { rgba2hex } from '@common/function';

export class Color {

  public opacity: number | string;
  
  constructor(
    public red: number,
    public green: number,
    public blue: number,
    opacity: number = 1,
  ) {
    this.setOpacity(opacity);
  }

  setOpacity(opacity: number) {
    const value = opacity > 1 ? (opacity / 100).toFixed(2) : opacity;
    this.opacity = value;
    return this;
  }

  toString() {
    return `rgba(${this.red}, ${this.green}, ${this.blue}, ${this.opacity})`;
  }

  toHex() {
    return rgba2hex(this.toString());
  }

}


/**
 * 
 * `R`, `G`, `B` Each value can be represented as a <number> between 0 and 255, 
 * a <percentage> between 0% and 100%, or the keyword none (equivalent to 0% in this case). 
 * These values represent the red, green, and blue channels, respectively.
 * @param {number} red 
 * @param {number} green 
 * @param {number} blue 
 * @param {number} [opacity=1] An <alpha-value> representing the alpha channel value of the color, 
 * where the number `0` corresponds to `0%` (fully transparent) and `1` corresponds to `100%` (fully opaque). 
 * Additionally, the keyword `none` can be used to explicitly specify no alpha channel. 
 * If the A channel value is not explicitly specified, it defaults to `100%`. 
 * If included, the value is preceded by a slash `(/)`.
 * 
 * @returns 
 */
function rgba(red: number, green: number, blue: number, opacity: number = 1) {
  let alpha = opacity > 1 ? Number((opacity / 100).toFixed(2)) : opacity;

  const result = {
    setOpacity: (opacity: number) => {
      alpha = opacity > 1 ? Number((opacity / 100).toFixed(2)) : opacity;
      return result;
    },
    toString: () => {
      return `rgba(${red}, ${green}, ${blue}, ${alpha})`;
    }
  }

  return result;
}


/**
 * The oklch() functional notation expresses a given color in the Oklab color space. 
 * oklch() is the cylindrical form of oklab(), using the same L axis, 
 * but with polar Chroma (C) and Hue (h) coordinates.
 * 
 * @param {number} l The `l` channel value is resolved to a `<number>` between 0 and 1, inclusive.
 * @param {number} c The `c` channel value is resolved to a `<number>` between 0 and 0.4, inclusive.
 * @param {number} h The `h` channel value is resolved to a `<number>` between 0 and 360, inclusive.
 * @param {number} alpha The `alpha` channel is resolved to a `<number>` between 0 and 1, inclusive.
 */
function oklch(l: number | string, c: number, h: number, alpha: number = 1) {
  alpha = alpha > 1 ? Number((alpha / 100).toFixed(2)) : alpha;

  const result = {
    setOpacity: (opacity: number) => {
      alpha = opacity > 1 ? Number((opacity / 100).toFixed(2)) : opacity;
      return result;
    },
    toString: () => {
      return `oklch(${l} ${c} ${h} / ${alpha})`;
    }
  };

  return result;
}


const colors = {
  slate: {
    50:  rgba(248,250,252),
    100: rgba(241,245,249),
    200: rgba(226,232,240),
    300: rgba(203,213,225),
    400: rgba(148,163,184),
    500: rgba(100,116,139),
    600: rgba(71,85,105),
    700: rgba(51,65,85),
    800: rgba(30,41,59),
    900: rgba(15,23,42),
    950: rgba(2,6,23),
  },

  gray: {
    50:  rgba(249,250,251),
    100: rgba(243,244,246),
    200: rgba(229,231,235),
    300: rgba(209,213,219),
    400: rgba(156,163,175),
    500: rgba(107,114,128),
    600: rgba(75,85,99),
    700: rgba(55,65,81),
    800: rgba(31,41,55),
    900: rgba(17,24,39),
    950: rgba(3,7,18),
  },
  
  zinc: {
    50:  rgba(250,250,250),
    100: rgba(244,244,245),
    200: rgba(228,228,231),
    300: rgba(212,212,216),
    400: rgba(161,161,170),
    500: rgba(113,113,122),
    600: rgba(82,82,91),
    700: rgba(63,63,70),
    800: rgba(39,39,42),
    900: rgba(24,24,27),
    950: rgba(9,9,11),
  },
  
  neutral: {
    50:  rgba(250,250,250),
    100: rgba(245,245,245),
    200: rgba(229,229,229),
    300: rgba(212,212,212),
    400: rgba(163,163,163),
    500: rgba(115,115,115),
    600: rgba(82,82,82),
    700: rgba(64,64,64),
    800: rgba(38,38,38),
    900: rgba(23,23,23),
    950: rgba(10,10,10),
  },
  
  stone: {
    50:  rgba(250,250,249),
    100: rgba(245,245,244),
    200: rgba(231,229,228),
    300: rgba(214,211,209),
    400: rgba(168,162,158),
    500: rgba(120,113,108),
    600: rgba(87,83,78),
    700: rgba(68,64,60),
    800: rgba(41,37,36),
    900: rgba(28,25,23),
    950: rgba(12,10,9),
  },

  red: {
    50:  rgba(254, 242, 242),
    100: rgba(254,226,226),
    200: rgba(254,202,202),
    300: rgba(252,165,165),
    400: rgba(248,113,113),
    500: rgba(239,68,68),
    600: rgba(220,38,38),
    700: rgba(185,28,28),
    800: rgba(153,27,27),
    900: rgba(127,29,29),
    950: rgba(69,10,10),
  },
  
  orange: {
    50:  rgba(255,247,237),
    100: rgba(255,237,213),
    200: rgba(254,215,170),
    300: rgba(253,186,116),
    400: rgba(251,146,60),
    500: rgba(249,115,22),
    600: rgba(234,88,12),
    700: rgba(194,65,12),
    800: rgba(154,52,18),
    900: rgba(124,45,18),
    950: rgba(67,20,7),
  },
  
  amber: {
    50:  rgba(255,251,235),
    100: rgba(254,243,199),
    200: rgba(253,230,138),
    300: rgba(252,211,77),
    400: rgba(251,191,36),
    500: rgba(245,158,11),
    600: rgba(217,119,6),
    700: rgba(180,83,9),
    800: rgba(146,64,14),
    900: rgba(120,53,15),
    950: rgba(69,26,3),
  },

  yellow: {
    50:  rgba(254,252,232),
    100: rgba(254,249,195),
    200: rgba(254,240,138),
    300: rgba(253,224,71),
    400: rgba(250,204,21),
    500: rgba(234,179,8),
    600: rgba(202,138,4),
    700: rgba(161,98,7),
    800: rgba(133,77,14),
    900: rgba(113,63,18),
    950: rgba(66,32,6),
  },
  
  lime: {
    50:  rgba(247,254,231),
    100: rgba(236,252,203),
    200: rgba(217,249,157),
    300: rgba(190,242,100),
    400: rgba(163,230,53),
    500: rgba(132,204,22),
    600: rgba(101,163,13),
    700: rgba(77,124,15),
    800: rgba(63,98,18),
    900: rgba(54,83,20),
    950: rgba(26,46,5),
  },

  green: {
    50:  rgba(240,253,244),
    100: rgba(220,252,231),
    200: rgba(187,247,208),
    300: rgba(134,239,172),
    400: rgba(74,222,128),
    500: rgba(34,197,94),
    600: rgba(22,163,74),
    700: rgba(21,128,61),
    800: rgba(22,101,52),
    900: rgba(20,83,45),
    950: rgba(5,46,22),
  },
  
  emerald: {
    50:  rgba(236,253,245),
    100: rgba(209,250,229),
    200: rgba(167,243,208),
    300: rgba(110,231,183),
    400: rgba(52,211,153),
    500: rgba(16,185,129),
    600: rgba(5,150,105),
    700: rgba(4,120,87),
    800: rgba(6,95,70),
    900: rgba(6,78,59),
    950: rgba(2,44,34),
  },
  
  teal: {
    50:  rgba(240,253,250),
    100: rgba(204,251,241),
    200: rgba(153,246,228),
    300: rgba(94,234,212),
    400: rgba(45,212,191),
    500: rgba(20,184,166),
    600: rgba(13,148,136),
    700: rgba(15,118,110),
    800: rgba(17,94,89),
    900: rgba(19,78,74),
    950: rgba(4,47,46),
  },
  
  cyan: {
    50:  rgba(236,254,255),
    100: rgba(207,250,254),
    200: rgba(165,243,252),
    300: rgba(103,232,249),
    400: rgba(34,211,238),
    500: rgba(6,182,212),
    600: rgba(8,145,178),
    700: rgba(14,116,144),
    800: rgba(21,94,117),
    900: rgba(22,78,99),
    950: rgba(8,51,68),
  },
  
  sky: {
    50:  rgba(240,249,255),
    100: rgba(224,242,254),
    200: rgba(186,230,253),
    300: rgba(125,211,252),
    400: rgba(56,189,248),
    500: rgba(14,165,233),
    600: rgba(2,132,199),
    700: rgba(3,105,161),
    800: rgba(7,89,133),
    900: rgba(12,74,110),
    950: rgba(8,47,73),
  },
  
  blue: {
    50:  rgba(239,246,255),
    100: rgba(219,234,254),
    200: rgba(191,219,254),
    300: rgba(147,197,253),
    400: rgba(96,165,250),
    500: rgba(59,130,246),
    600: rgba(37,99,235),
    700: rgba(29,78,216),
    800: rgba(30,64,175),
    900: rgba(30,58,138),
    950: rgba(23,37,84),
  },
  
  indigo: {
    50:  rgba(238,242,255),
    100: rgba(224,231,255),
    200: rgba(199,210,254),
    300: rgba(165,180,252),
    400: rgba(129,140,248),
    500: rgba(99,102,241),
    600: rgba(79,70,229),
    700: rgba(67,56,202),
    800: rgba(55,48,163),
    900: rgba(49,46,129),
    950: rgba(30,27,75),
  },

  violet: {
    50:  rgba(245,243,255),
    100: rgba(237,233,254),
    200: rgba(221,214,254),
    300: rgba(196,181,253),
    400: rgba(167,139,250),
    500: rgba(139,92,246),
    600: rgba(124,58,237),
    700: rgba(109,40,217),
    800: rgba(91,33,182),
    900: rgba(76,29,149),
    950: rgba(46,16,101),
  },
  
  purple: {
    50:  rgba(250,245,255),
    100: rgba(243,232,255),
    200: rgba(233,213,255),
    300: rgba(216,180,254),
    400: rgba(192,132,252),
    500: rgba(168,85,247),
    600: rgba(147,51,234),
    700: rgba(126,34,206),
    800: rgba(107,33,168),
    900: rgba(88,28,135),
    950: rgba(59,7,100),
  },
  
  fuchsia: {
    50:  rgba(253,244,255),
    100: rgba(250,232,255),
    200: rgba(245,208,254),
    300: rgba(240,171,252),
    400: rgba(232,121,249),
    500: rgba(217,70,239),
    600: rgba(192,38,211),
    700: rgba(162,28,175),
    800: rgba(134,25,143),
    900: rgba(112,26,117),
    950: rgba(74,4,78),
  },
  
  pink: {
    50:  rgba(253,242,248),
    100: rgba(252,231,243),
    200: rgba(251,207,232),
    300: rgba(249,168,212),
    400: rgba(244,114,182),
    500: rgba(236,72,153),
    600: rgba(219,39,119),
    700: rgba(190,24,93),
    800: rgba(157,23,77),
    900: rgba(131,24,67),
    950: rgba(80,7,36),
  },

  rose: {
    50:  rgba(255,241,242),
    100: rgba(255,228,230),
    200: rgba(254,205,211),
    300: rgba(253,164,175),
    400: rgba(251,113,133),
    500: rgba(244,63,94),
    600: rgba(225,29,72),
    700: rgba(190,18,60),
    800: rgba(159,18,57),
    900: rgba(136,19,55),
    950: rgba(76,5,25),
  }
}


type Step = 50 | 100 | 200 | 300 | 400 | 500 | 600 | 700 | 800 | 900 | 950;

/**
 * ## Colors
 * ***Using and customizing the color palette in Tailwind CSS projects.***
 * @description Tailwind CSS includes a vast, beautiful color palette out of the box, carefully crafted by expert designers and suitable for a wide range of different design styles.
 * @usageNotes 
 * 
 * ```ts
 * Colors.Red(50)
 * Colors.Red(50, 0.1)
 * Colors.Red(50, 10)
 * ```
 */
export class Colors {

  /** @access private */
  private constructor() {}

  static Inherit = 'inherit';
  static Current = 'currentcolor';
  static Transparent = 'transparent';
  static Black = '#000';
  static White = '#fff';

  static Red = function(step: Step, opacity = 1) {
    return colors.red[step].setOpacity(opacity).toString();
  }

  static Orange = function(step: Step, opacity = 1) {
    return colors.orange[step].setOpacity(opacity).toString();
  }

  static Amber = function(step: Step, opacity = 1) {
    return colors.amber[step].setOpacity(opacity).toString();
  }

  static Yellow = function(step: Step, opacity = 1) {
    return colors.yellow[step].setOpacity(opacity).toString();
  }

  static Lime = function(step: Step, opacity = 1) {
    return colors.lime[step].setOpacity(opacity).toString();
  }

  static Green = function(step: Step, opacity = 1) {
    return colors.green[step].setOpacity(opacity).toString();
  }

  static Emerald = function(step: Step, opacity = 1) {
    return colors.emerald[step].setOpacity(opacity).toString();
  }

  static Teal = function(step: Step, opacity = 1) {
    return colors.teal[step].setOpacity(opacity).toString();
  }

  static Cyan = function(step: Step, opacity = 1) {
    return colors.cyan[step].setOpacity(opacity).toString();
  }

  static Sky = function(step: Step, opacity = 1) {
    return colors.sky[step].setOpacity(opacity).toString();
  }

  static Blue = function(step: Step, opacity = 1) {
    return colors.blue[step].setOpacity(opacity).toString();
  }

  static Indigo = function(step: Step, opacity = 1) {
    return colors.indigo[step].setOpacity(opacity).toString();
  }

  static Violet = function(step: Step, opacity = 1) {
    return colors.violet[step].setOpacity(opacity).toString();
  }

  static Purple = function(step: Step, opacity = 1) {
    return colors.purple[step].setOpacity(opacity).toString();
  }

  static Fuchsia = function(step: Step, opacity = 1) {
    return colors.fuchsia[step].setOpacity(opacity).toString();
  }

  static Pink = function(step: Step, opacity = 1) {
    return colors.pink[step].setOpacity(opacity).toString();
  }

  static Rose = function(step: Step, opacity = 1) {
    return colors.rose[step].setOpacity(opacity).toString();
  }

  static Slate = function(step: Step, opacity = 1) {
    return colors.slate[step].setOpacity(opacity).toString();
  }

  static Gray = function(step: Step, opacity = 1) {
    return colors.gray[step].setOpacity(opacity).toString();
  }

  static Zinc = function(step: Step, opacity = 1) {
    return colors.zinc[step].setOpacity(opacity).toString();
  }

  static Neutral = function(step: Step, opacity = 1) {
    return colors.neutral[step].setOpacity(opacity).toString();
  }

  static Stone = function(step: Step, opacity = 1) {
    return colors.stone[step].setOpacity(opacity).toString();
  }
  
}

// console.log(Colors.Red(950, 0.3));
// console.log(`${oklch('59.69%', 0.156, 49.77, .5)}`);
// console.log(`${rgba(248, 250, 252)}`);
// console.log(`${rgba(241, 245, 249)}`);
