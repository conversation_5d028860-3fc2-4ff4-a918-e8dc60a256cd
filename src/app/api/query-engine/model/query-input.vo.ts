import { DtType } from '@common/service/query-engine'

type Condition = '>' | '<' | '=' | '>=' | '<=' | '!=' | 'not null' | 'in' | 'and' | 'or'

class DimensionValueVo {
  /** 编码【数字或者英文字符】 */
  key?: string

  /** 码值【中文名称可用来展示】 */
  value?: string

  extendName?: string

  relValue?: string
}

export interface FilterItemVo {
  /** 运算类型 逻辑运算 1,条件运算 2 */
  conditionType?: 1 | 2 | number

  /** 运算标识;条件运算公式 [>,<,=,>=,<=,!=,not null,in],逻辑运算公式 [and,or] */
  condition?: Condition | string

  /// region 条件运算时生效

  /** 指标id/维度id */
  id?: number

  /** 维度英文名 */
  extendName?: string

  /** 筛选值 */
  value?: DimensionValueVo[]

  /** 筛选值类型 指标(metrics)/维度(dimension) */
  valueType?: string
}

export interface FilterVo {
  /** 筛选标识 */
  type?: string

  /** 筛选项列表 */
  items?: FilterItemVo[]
}

export interface PageVo {
  /** 是否分页 true 分页返回，false 不分页 */
  pageState: boolean

  /** 页码 从1开始 */
  pageNum: number

  /** 每页数量 */
  pageSize: number

  /** 如果不做分页返回多少行数据，默认1000 */
  limit: number
}

export interface QueryDt {
  /** 时间开始分区值，格式：yyyy-MM-dd */
  startTime: string

  /** 时间结束分区值，格式：yyyy-MM-dd */
  endTime: string
}

export interface DimensionVo {
  /** 维度id */
  id?: number

  /** 英文名称 */
  extendName?: string

  predefineCompareType?: string[]
}

export interface MetricVo {
  /** 指标id */
  id?: number

  /** 指标别名 */
  aliasName?: string

  showName?: string;

  /** 英文名称 */
  extendName?: string

  /** 指标类型 */
  type?: number

  /**
   * @name 指标说明
   */
  bizExpression?: string

  /**
   * @name 标签名称
   */
  tagName?: string

  /**
   * @name [自选维度指标新增字段] 默认推荐指标
   * - 1 推荐指标
   * - 0 不是
   */
  recommend?: 0 | 1 | number;

  /**
   * @name [自选维度指标新增字段] 是否用户选中，
   * - 1 选中
   * - 其他 未选中
   */
  display?: 0 | 1 | number

  /**
   * @name [自选维度指标新增字段] 指标展示顺序
   */
  displayOrder?: number

  /**
   * @name [自选维度指标新增字段] 标签排序
   */
  tagOrder?: number

  /** 自定义计算类型 */
  customType?: string

  /** 比例维度,占总体时传空数组即可 */
  proportionDimension?: DimensionVo[]

  /** 用户自定义名称 */
  userDefExtendName?: string

  /** 用户自定义展示的图利名称 */
  userDefAliasName?: string

  /** 筛选条件【新增：用于对比分析】 */
  filter?: FilterVo;
  
  yAxis?: number;

  dataUnit?: string;
}

export interface OrderVo {
  /** 指标或者维度id */
  id: number

  /** 指标或者维度英文名 */
  extendName: string

  /** 是指标还是维度 1 指标，2 维度 */
  source: number

  /** 排序逻辑 asc,desc */
  type: 'asc' | 'desc'
}

export interface HavingVo {
  /** 指标id */
  id: number

  /** 指标英文名 */
  extendName: string

  /** 运算公式 [>,<,=,>=,<=,!=,not null,in] */
  condition: string

  /** 筛选值 */
  value: string
}

export interface DataFillConfig {
  /** 是否开启 1 开启，0 关闭 */
  open: number

  /** 补数范围
   * - 1 根据查询结果的最大值和最小值
   * - 2 根据查询分区的最大值和最小值 【年周月会按照分区筛选来补数，其他时间维度按照时间枚举值来处理】
   */
  fillRangeType: number

  /** 数据填充类型 0 补零；1 补 null */
  fillValue: number
}

export interface QueryInputVo {
  /**
   * @default day
   * 时间分区查询类型
   * - day (日)
   * - month (月)
   * - week (周)
   * - 默认是日
   */
  dtType?: DtType

  /** 时间分区查询【必传参数】 */
  dt?: QueryDt

  /** 时间对比分区查询 */
  compareDt?: QueryDt

  /** 维度数组 */
  dimensions?: DimensionVo[]

  /** 额外的维度组合 */
  extraDimensionGroups?: DimensionVo[][]

  extraDimensionsExcludeFilter?: string[][];

  /** 指标数组 */
  metrics?: MetricVo[]

  // 这里可能会有其他复杂运算 and or 等
  /** 筛选条件【用于指标筛选】 */
  filter?: FilterVo;

  metricsFilter?: FilterVo;

  /** 补数配置参数 */
  dataFillConfig?: DataFillConfig

  /** 排序字段[按照参数默认传入顺序排序] */
  orders?: Partial<OrderVo>[]

  /** 筛选条件【用于维度筛选】 */
  // having?: HavingVo[];

  /** 分页参数 */
  // pageable?: PageVo;

  /**
   * 场景
   * 1. 数据大盘
   * 2. A/B实验分析
   * 3. 分群数据指标
   * 4. 驾驶舱
   */
  scene?: number
  compateDt?: any
  queryType?: string
  userDefExpressions?: UserDefExpression[]
  outAggDimensions?: DimensionVo[];
  useLocalQuery?: boolean;
  accelerateFilter?: boolean;
  metricsOrders?: Array<{
    extendName: string,
    type: 'desc' | 'asc'
  }>
}

export class UserDefExpression {
  /** 用户自定义SQL */
  expression: string

  /** 用户自定义名称 */
  userDefExtendName: string

  /** 用户自定义展示的图利名称 */
  userDefAliasName: string
}

export interface QueryInputExtendVo extends QueryInputVo {
  /**
   * 展示大盘[总体值] 1 查询大盘数据，0 不查询大盘数据
   */
  showTotal: number

  /** 展示大盘维度英文名 */
  totalExtendName?: string
}
