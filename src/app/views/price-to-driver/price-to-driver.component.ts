import { ChangeDetectionStrategy, Component, signal } from '@angular/core'
import { FormsModule } from '@angular/forms'
import { HeadingComponent } from '@shared/components/heading'
import { RadioModule } from '@shared/modules/headless'
import * as L from 'leaflet'
import * as h3 from 'h3-js'
import 'leaflet/dist/leaflet.css'
import { AfterViewInit } from '@angular/core'
import { LeafletModule } from '@bluehalo/ngx-leaflet'

@Component({
  selector: 'app-price-to-driver',
  imports: [FormsModule, HeadingComponent, RadioModule, LeafletModule],
  templateUrl: './price-to-driver.component.html',
  styles: ``,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PriceToDriverComponent implements AfterViewInit {
  type = signal('1')

  ngAfterViewInit() {}
  map: L.Map | null = null
  mapOptions: L.MapOptions = {
    zoom: 15,
    center: [39.9042, 116.4074] as L<PERSON>LatLngExpression,
    layers: [
      L.tileLayer('https://webrd0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=7&x={x}&y={y}&z={z}', {
        subdomains: ['1', '2', '3', '4'],
        opacity: 0.7,
      }),
    ],
  }
  // 目标 H3 单元格 ID（北京区域分辨率9的单元）
  targetCellIds = [
    '894e4dac7ffffff', // 主单元
    '894e4dac3ffffff', // 相邻单元
    '8a309bb6338ffff', // 相邻单元
    '8900e19a68bffff', // 相邻单元
  ]

  onMapReady(map: L.Map) {
    this.map = map
    this.renderH3Cells(this.targetCellIds)
    this.addMapInteractions()
  }

  // 核心：将H3单元格转为Leaflet多边形
  private cellToPolygon(cellId: string): L.LatLngExpression[] {
    const boundary = h3.cellToBoundary(cellId)
    return boundary.map(point => [point[1], point[0]]) // [lat, lng] → [lng, lat]
  }

  renderH3Cells(cellIds: string[]) {
    const geoJson = {
      type: 'FeatureCollection' as const,
      features: cellIds.map(id => ({
        type: 'Feature' as const,
        properties: { id },
        geometry: {
          type: 'Polygon' as const,
          coordinates: [this.cellToPolygon(id)],
        },
      })),
    }

    L.geoJSON(geoJson, {
      style: () => ({
        color: '#ff0000',
        fillColor: '#ff0000',
        fillOpacity: 0.4,
        weight: 2,
      }),
      onEachFeature: (feature, layer) => {
        layer.bindPopup(`H3 ID: ${feature.properties?.['id']}`)
      },
    }).addTo(this.map!)
  }

  addMapInteractions() {
    this.map!.on('click', (e: L.LeafletMouseEvent) => {
      const resolution = 9
      const clickedCellId = h3.latLngToCell(e.latlng.lat, e.latlng.lng, resolution)

      // 绘制红色高亮地块
      const polygon = L.polygon(this.cellToPolygon(clickedCellId), {
        color: '#ff0000',
        fillOpacity: 0.7,
      })

      polygon.addTo(this.map!).bindPopup(`点击生成: ${clickedCellId}`).openPopup()
    })
  }
}
