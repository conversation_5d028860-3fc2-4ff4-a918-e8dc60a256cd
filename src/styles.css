/* You can add global styles to this file, and also import other style files */
/**
 * This injects Tailwind's base styles and any base styles registered by
 * plugins, like Normalize.css
 * disable Preflight, preflight: false,
 */
@import 'tailwindcss';

@custom-variant dark (&:where(.dark, .dark *));

@import './assets/styles/theme.css';
@import './assets/styles/animtion.css';
@import './assets/styles/shadow.css';
@import './assets/styles/override.css';
@import './assets/styles/icon-button.css';
@import './assets/styles/modal.css';
@import './assets/styles/radio.css';


@utility link {
  @apply text-xs 
    cursor-pointer whitespace-nowrap leading-none
    hover:text-primary;

  &.active {
    @apply text-primary underline underline-offset-3;
  }
}

@utility input-base {
  @apply flex-1 min-w-0 overflow-hidden block w-full h-full p-1.5 rounded-lg text-xs! sm:text-sm bg-gray-50 border border-gray-300 text-[#0a1220]/60 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500;
}

@utility btn-confirm {
  @apply h-7 px-4 rounded-sm text-xs text-[#5182e4] bg-[#5182e4]/10 hover:bg-[#5182e4]/20 transition-all duration-300;
}

@utility expand-btn {
  @apply group-hover:animate-[scale_0.7s_ease-in-out_infinite];
}

@utility search-box {
  padding: 8px;

  & input {
    width: 188px;
    margin-bottom: 8px;
    display: block;
  }

  & button {
    width: 90px;
  }
}

@utility search-button {
  @apply mr-2;
}

@utility explain {
  @apply text-xs text-neutral-400;
}

@utility tools-bar {
  @apply flex flex-col items-center justify-center px-3 h-screen sticky top-0 bottom-0 py-20;

  & .btn {
    @apply flex flex-col items-center justify-center gap-y-1 w-full;
  }

  & .btn .label {
    @apply select-none text-xs text-slate-500 scale-75 -translate-y-1 whitespace-nowrap origin-top;
  }
}

@utility btn {
  .tools-bar & {
    @apply flex flex-col items-center justify-center gap-y-1 w-full;
  }

  .tools-bar & .label {
    @apply select-none text-xs text-slate-500 scale-75 -translate-y-1 whitespace-nowrap origin-top;
  }
}

@utility label {
  .tools-bar .btn & {
    @apply select-none text-xs text-slate-500 scale-75 -translate-y-1 whitespace-nowrap origin-top;
  }
}

@utility filter-none {
  filter: none;
}

@utility filter-grayscale {
  filter: grayscale(100%);
}

@utility filter-container {
  @apply
    flex flex-wrap items-center gap-3 
    py-5 px-4 rounded 
    bg-blue-50/70 
    shadow-md shadow-blue-100/80;
}

@utility filter-container-grid {
  @apply
    grid grid-flow-col gap-3 max-2xl:gap-1
    py-5 px-4 rounded 
    bg-blue-50/70 
    shadow-md shadow-blue-100/80;
}

@utility card-theme {
  &.orange {
    @apply bg-linear-to-b from-orange-200/50 via-white via-25% shadow-md shadow-orange-200/70 
  }
  &.teal {
    @apply bg-linear-to-b from-teal-600/30 via-white via-25% shadow-md shadow-teal-600/20 
  }
  &.blue {
    @apply bg-linear-to-b from-blue-600/20 via-white via-25% shadow-md shadow-blue-600/20 
  }
  

  &.orange .tab-radio {
    @apply border-orange-300
  }
  &.orange .tab-radio.active {
    @apply bg-orange-300 text-white;
  }

  &.blue .tab-radio {
    @apply border-blue-300
  }
  &.blue .tab-radio.active {
    @apply bg-blue-300 text-white;
  }

  &.teal .tab-radio {
    @apply border-teal-300
  }
  &.teal .tab-radio.active {
    @apply bg-teal-300 text-white;
  }
}

@utility line-input {
  @apply
    inline-flex items-center gap-x-2 
    w-full h-8 px-2.5 
    border border-neutral-300/80
    transition-colors rounded-xs cursor-pointer
    outline-primary/0 appearance-none!
    focus:outline-2
    focus:outline-primary/20
    focus:border-primary/75 
    hover:border-primary/75;
}
/**
 * Tailwind will automatically move the CSS within any @layer directive to the same place as the corresponding @tailwind rule, 
 * so you don’t have to worry about authoring your CSS in a specific order to avoid specificity issues.
 */

@layer base {
  h1 {
  }
  h2 {
  }

  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Demonstrate a "mostly customized" scrollbar
  * (won't be visible otherwise if width/height is specified) */
  .customized-scrollbar::-webkit-scrollbar {
    width: 5px;
    height: 8px;
    @apply bg-neutral-100
  }

  /* Add a thumb */
  .customized-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-neutral-200
  }
}


.fixed-widgets {
  @apply 
    fixed top-0 right-0 bottom-0 z-40
    flex flex-col gap-3 w-20 pt-40 pb-10 pl-3 pointer-events-none;
}


._bgWaterMark{
  @apply fixed inset-0 z-99999 bg-repeat pointer-events-none;
  background-size: 300px 150px;
}


/* highcharts 标注 */
.highcharts-plot-line-label {
  transform: translate3d(-10px, 8px, 0);
}

.guild-label-left {
  @apply
    relative
    inline-flex items-center
    h-[15px] pl-[3px]
    rounded-tl-sm rounded-bl-sm
    bg-red-500
    text-white text-xs
    leading-[15px] whitespace-nowrap
    cursor-pointer
    after:content-[''] after:absolute
    after:border-[7px] after:border-solid
    after:top-0 after:bottom-0 after:-right-[14px] 
    after:border-l-red-500 after:border-t-transparent 
    after:border-r-transparent after:border-b-transparent;
}