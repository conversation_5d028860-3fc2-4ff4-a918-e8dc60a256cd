import { QueryOutputVo, QueryDt, QueryOutputHeaderVo } from '@api/query-engine/model';
import { getColumnFields, getDimensionField, getNumberFields } from '@common/chart';
import { createValueElement, formatMileageLabel, groupBy, sortBy, toDecimals, toNumber } from '@common/function';
import { DtType } from '@common/service/query-engine';
import { Colors } from '@common/const';

export class ItemStyle {
  color = 'auto';
  borderColor = '#000';
  borderWidth = 0;
  borderType = 'solid';
  borderRadius = 0;
  shadowOffsetX = 0;
  shadowOffsetY = 0;
  opacity = 0.3;

  decal = {
    symbol: 'rect',
    symbolSize: 1,
    symbolKeepAspect: true,
    color: 'rgba(0, 0, 0, 0.2)',
    dashArrayX: [1, 0],
    dashArrayY: [4, 3],
    dirty: false,
    backgroundColor: null,
    rotation: -0.5235987755982988,
    maxTileWidth: 512,
    maxTileHeight: 512,
  };
}

class SeriesItem {
  name: string;
  type = 'bar';
  xAxisIndex: number;
  data: any[];
  itemStyle: ItemStyle;
}



export class BarChart {
  title = {
    text: '',
    textAlign: 'left',
    left: 'center',
    textStyle: {
      fontSize: 14
    }
  };

  tooltip = {
    trigger: 'axis',
    axisPointer: { type: 'shadow' },
    confine: true,
    padding: 0,
    appendTo: 'body',
    position: 'left',
    extraCssText: 'box-shadow: unset',
    backgroundColor: 'rgba(0,0,0,0)',
    borderWidth: 0,
    formatter: (params) => {
      const result = [] as string[];
      const map = new Map();

      params.forEach(item => {
        if (map.has(item.seriesName)) {
          const arr = map.get(item.seriesName) as any[];
          arr.push(item);
          arr.reverse();
        } else {
          map.set(item.seriesName, [item]);
        }
      });

      const merged = [...map.values()].flat(1);
      const rows = [];

      // 生成table header
      result.push('<table class="absolute right-1/4 bg-white shadow-1 rounded overflow-hidden">');
      result.push(`
        <tr>
          <th colspan="4" class="text-left border-b border-neutral-200 px-3 py-1">${
            this.isMileage ? formatMileageLabel(merged.at(0).name) : merged.at(0).name
          }</th>
        </tr>
      `);

      // 生成表头
      result.push(`<tr class="bg-neutral-100 border-b border-neutral-200">`);
      result.push(`<th class="py-1 px-5">指标</th>`);
      
      merged.forEach((item) => {
        const nameTemplate = item.seriesName
          .replace(/\(|\)|~/g, '')
          .replace(/\d{4}-\d{2}-\d{2}/g, '');

        result.push(`
          <th class="py-1 px-5">${nameTemplate}</th>
        `);
      })
      result.push(`<th class="py-1 px-5">较对比期</th>`);
      result.push(`</tr>`);

      // 生成表格所需的每个单元格的内容
      merged.forEach((item, index) => {
        const { data } = item;
        const list = data.items.sort((a, b) => a.primary ? -1 : 0);
        // const category = item.seriesName
        //   .replace(/\(|\)|~/g, '')
        //   .replace(/\d{4}-\d{2}-\d{2}/g, '');
        
        list.forEach((m, i) => {
          let content = m.value || '-';

          if (index === 0) {
            const isGroup = /\|/.test(m.name);
            const obj = { content: m.name } as any;

            if (isGroup) {
              const [groupName, name] = (<string>m.name).split('|');
              obj.isGroup = true;
              obj.groupName = groupName;
              obj.content = name;
            }

            rows.push([obj])
          }

          rows[i].push({ 
            // category, 
            content, ...m
          })
        })
      });

      (() => {
        const first = rows.find(row => row.find(item => item.isGroup === true));
        const groupCount = rows.map(row => row.filter(item => item.isGroup === true)).flat(1).length;
        
        if (groupCount > 0) {
          first.at(0).rowspan = groupCount;
          rows.forEach(row => {
            if (!row.at(0).isGroup) {
              row.at(0).colspan = 2;
            }
          })

          // 如果存在分组，则替换指标单元格
          result[3] = `<th class="py-1 px-5" colspan="2">指标</th>`;
        }
      })();
      
      // 计算较对比期 [diff(diffRatio)]
      rows.forEach((row, index) => {
        const [, { isPercent, diffValue, diffRatio }] = row;
        let content = '';

        // console.table(row);
        
        if (isPercent) {
          content = `
            ${!isFinite(diffValue) ? '-' : createValueElement(toDecimals(diffValue), '{n}pp')}
          `.replace(/\n+\s+/g,'');
        } else {
          content = `
            ${!isFinite(diffValue) ? '-' : createValueElement(diffValue, '{n}', true, '-')}  
            ${!isFinite(diffRatio) ? '' : createValueElement(diffRatio, '({n}%)', true, '')}  
          `.replace(/\n+\s+/g,'');
        }

        rows[index].push({ content });
      })

      // output and render
      rows.forEach((row, index) => {
        const bgColor = index % 2 === 1 ? 'bg-neutral-100' : '';
        // trace('[row]', row);
        
        result.push(`<tr class="${bgColor}">`);
        row.forEach((item, i) => {
          const textAlign = i === 0 ? 'text-left' : 'text-center';

          if (item?.rowspan) {
            result.push(`
              <td class="py-1 px-5 max-w-24 whitespace-normal ${textAlign}" rowspan="${item.rowspan}">
                ${item.groupName}
              </td>
            `);
          }

          result.push(`
            <td class="py-1 px-5 ${textAlign}" colspan="${item?.colspan || null}">
              ${item.content}
            </td>
          `);
        })
        result.push(`</tr>`);
      })

      result.push('</table>');
      return result.join('');
    }
  };

  legend = {
    top: 'bottom',
    orient: 'vertical'
  };

  color: string[];
  grid = {
    left: '3%',
    right: '4%',
    // bottom: '3%',
    containLabel: true
  };
  xAxis = { 
    type: 'value', 
    // splitNumber: 3, 
    boundaryGap: [0, '20%'],
    // max: function (value) {
    //   const magnitude = Math.pow(10, Math.floor(Math.log10(value.max)));
    //   const v = value.max + magnitude;
    //   const a = v.toString().substr(0,2);
    //   const b = Number(a) / 10;
    //   const c = Math.round(b);
    //   const d = v.toString().substring(1).replace(/\d/g, '0');
    //   const e = Number(`${c}${d}`);
  
    //   console.clear();
    //   console.log(value.max, magnitude, b, c, e);
      
    //   return e;
    // }
  };
  yAxis = { type: 'category', data: [] as string[] };
  
  series: SeriesItem[];

  public metricsOrder: string[];
  public primaryExtendName: string;
  public isMileage: boolean;
  public dtType: DtType;

  init(
    properties: QueryOutputVo, 
    dt: QueryDt, 
    compareDt: QueryDt, 
  ) {
    const { headers, data, compareData } = properties;
    const categories = this.getCategories(headers, data);
    const series = this.getSeries(headers, data, `当前期 (${dt.startTime}~${dt.endTime})`);
    const order = [...new Set(series.data.map(item => item.items.map(i => i.key)).flat(1))];
    const compareSeries = this.getSeries(headers, compareData, `对比期 (${compareDt.startTime}~${compareDt.endTime})`, order);
    compareSeries.itemStyle = new ItemStyle();

    (<any>series).label = {
      show: true,
      position: 'right',
      offset: [0, 3],
      formatter: function(params) {
        const primaryData = params.data.items.find(item => item.primary);

        if (primaryData) {
          const { diffValue, diffRatio, isPercent } = primaryData;
          const up = () => `{up|}`;
          const down = () => `{down|}`;
          const add = (value) => `{add|${value}}`;
          const sub = (value) => `{sub|${value}}`;
          const diffValueTemplate = diffValue > 0 
            ? `${up()}${add(`+${Intl.NumberFormat().format(diffValue)}`)}`
            : diffValue < 0 
              ? `${down()}${sub(`${Intl.NumberFormat().format(diffValue)}`)}`
              : diffValue;

          // const diffRatioTemplate = diffRatio > 0 
          //   ? add(`(+${diffRatio}%)`)
          //   : diffRatio < 0 
          //     ? sub(`(${diffRatio}%)`)
          //     : `(${diffRatio}%)`;

          return `${diffValueTemplate} `; // ${diffRatioTemplate}
        }
        return '';
      },
      rich: {
        up: {
          height: 20,
          align: 'left',
          backgroundColor: {
            image: 'assets/images/icons/up.png'
          },
        },
        down: {
          height: 20,
          align: 'left',
          backgroundColor: {
            image: 'assets/images/icons/down.png'
          },
        },
        add: {
          color: Colors.Red(600),
        },
        sub: {
          color: Colors.Green(500),
        }
      }
    }

    this.setSeries([series, compareSeries]);
    this.setCategories(categories);
  }

  protected getCategories(
    headers: { [key: string]: QueryOutputHeaderVo },
    data: { [key: string]: string }[]
  ) {
    if (!data) { return }

    const columnFields = getColumnFields(headers);
    const categories = columnFields.map(key => data.map(item => item[key])).flat(1);

    return [...new Set(categories)];
  }
  

  setCategories(value: string[]) {
    this.yAxis.data = value;
  }


  setSeries(series: SeriesItem[]) {
    this.series = series;
  }


  getSeries(
    headers: { [key: string]: QueryOutputHeaderVo },
    data: { [key: string]: string }[],
    name: string,
    order: string[] = []
  ) {
    const numberFields = getNumberFields(headers);
    const [dimensionField] = getDimensionField(headers);
    const groupData = groupBy(data, dimensionField);
    const series = new SeriesItem();

    series.name = name;
    series.data = [];
    
    Object.keys(groupData).sort(sortBy(order, null)).forEach((key) => {
      const obj = {
        items: []
      } as any;

      numberFields
        // .sort(sortBy(this.metricsOrder, null))
        .forEach(field => {
          const _v = toNumber(groupData[key][0][field]);
          const diffValue = toNumber(groupData[key][0][field+'_DIFF']);
          const diffRatio = toNumber(groupData[key][0][field+'_DIFF_RATIO']);
          const primary = field === this.primaryExtendName;
          const { aliasName, dataUnit } = headers[field];
          const isPercent = dataUnit === '%';
          const value = isPercent 
            ? createValueElement(toDecimals(_v), '{n}%')
            : Intl.NumberFormat().format(toDecimals(_v, 1));

          if (primary) {
            obj.value = toDecimals(_v, 1);
          }

          obj.items.push({
            // extendName: field,
            key: groupData[key][0][dimensionField], // 用于排序对比期
            name: aliasName,
            value,
            diffValue,
            diffRatio,
            isPercent,
            primary,
          });
        })
      
      series.data.push(obj);
    })

    return series;
  }


  getOption() {
    return this;
  }

}
