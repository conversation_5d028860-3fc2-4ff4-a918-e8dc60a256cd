import { NgClass } from '@angular/common';
import { RouterLink } from '@angular/router';
import { DomPortal, PortalModule } from '@angular/cdk/portal';
import { ChangeDetectionStrategy, Component, computed, ElementRef, inject, input, output, signal, TemplateRef, viewChild, ViewContainerRef } from '@angular/core';
import { toObservable } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { finalize, lastValueFrom, map } from 'rxjs';

import { isDev } from '@common/const';
import { CompareTime, Time } from '@common/class';
import { QueryEngineFormService } from '@common/service/query-engine';
import { IntersectionDirective } from '@common/directive';
import { AnalysisChart, DimensionMenuVo } from '@api/caerus/model';
import { QueryEngineApiService } from '@api/query-engine';
import { CaerusApiService } from '@api/caerus';
import { QueryInputVo, QueryOutputVo } from '@api/query-engine/model';
import { ModalService } from '@core/modal';
import { isNotEmpty, sortCategoriesFn3, windowResize } from '@common/function';
import { LegendControlService, LegendItemClickHandler } from '@common/service';
import { After, Confirmed } from '@common/decorator';
import { LineSpinComponent } from '@shared/components/line-spin';
import { IconDeleteComponent, IconDownloadComponent, IconEditComponent, IconEditContainerComponent, IconFullscreenComponent, IconFullscreenExitComponent } from '@shared/modules/icons';
import { BaseHighCharts } from '@common/chart/highcharts';
import { BasicLine, MultipleXAxis } from '@views/analysis/self-help/model/line';
import { BarStack, BarStackMultipleXAxis } from '@views/analysis/self-help/model/bar';
import { CardContentComponent } from '@shared/modules/card';
import { GraphComponent } from '@shared/components/graph';
import { CardModule } from '@shared/modules/card';

import { GraphListComponent } from '../../views/graph-list/graph-list.component';
import { ChartEditorComponent } from '../modal';


@Component({
  selector: 'app-graph-box',
  templateUrl: './graph-box.component.html',
  styleUrl: './graph-box.component.css',
  host: {
    '[class.cursor-move]': 'dragEnabled()',
    '(window:keyup.escape)': 'exitFullScreen()',
    '[class.selected]': 'selected()',
    '(click)': 'toggleSelect()'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgClass,
    FormsModule,
    RouterLink,
    PortalModule,
    NzButtonModule,
    NzToolTipModule,
    NzPopoverModule,
    NzCheckboxModule,
    CardModule,
    GraphComponent,
    LineSpinComponent,
    IconEditComponent,
    IconEditContainerComponent,
    IconFullscreenExitComponent,
    IconFullscreenComponent,
    IconDownloadComponent,
    IconDeleteComponent,
    IntersectionDirective,
  ],
  providers: [
    LegendControlService,
    QueryEngineFormService,
  ]
})
export class GraphBoxComponent {

  readonly modal = inject(NzModalService);
  readonly modalService = inject(ModalService);
  readonly apiService = inject(CaerusApiService);
  readonly queryEngineApiService = inject(QueryEngineApiService);
  readonly legendControlService = inject(LegendControlService);
  readonly viewContainer = inject(ViewContainerRef);
  readonly message = inject(NzMessageService);
  readonly parent = inject(GraphListComponent);
  readonly formService = inject(QueryEngineFormService);

  selected = input<boolean>();
  deleteMode = input<boolean>();
  data = input.required<AnalysisChart>();
  dragEnabled = input<boolean>();
  data$ = toObservable(this.data);
  onSelect = output();
  remove = output();
  
  cardContentRef = viewChild(CardContentComponent, { read: ElementRef });
  fullScreenContainerTpl = viewChild('fullScreenContainerTpl', { read: TemplateRef });
  domPortal = computed(() => new DomPortal(this.cardContentRef()));
  isFullScreen = signal(false);
  querying = signal(false);
  option = signal<BaseHighCharts>(null);
  chart: BaseHighCharts = null;
  filterLabel = signal<string>(null);
  filterLabelStr = signal<string>(null);
  chartType = signal<'line' | 'column'>('line');
  orderType = signal<'base' | 'compare'>(null);
  orderBy = signal<'desc' | 'asc'>('desc');
  noData = signal(false);
  errorMessage = signal<string>(null);
  analysisTypeMap = new Map<number, any>([
    [1, 'trend'],
    [2, 'constitute'],
    [3, 'comparison'],
  ]);


  hasAuth(username: string) {
    return (
      isDev() || 
      username === window.localStorage.getItem('caerus.auth.username.py') ||
      username === window.localStorage.getItem('caerus.auth.username')
    );
  }
  

  query(data: AnalysisChart = this.data()) {
    const body = JSON.parse(data.chartConfig);

    this.formService.setMetrics(body.metrics);
    
    if (data.analysisType === 2) {
      const metrics_copy = body.metrics.map(item => {
        return {
          ...item,
          customType: 'proportion',
          proportionDimension: [{ extendName: body.dtType }]
        };
      });
      body.metrics = body.metrics.concat(metrics_copy);
    }

    if (data.analysisType === 3) {
      body.queryType = 'compare';
    }

    body.dataFillConfig = {
      open: 1,
      fillRangeType: 1,
      fillValue: 1,
    };
    
    this.querying.set(true);
    this.noData.set(false);

    this.queryEngineApiService.search(body).pipe(
      finalize(() => this.querying.set(false)),
    ).subscribe(res => {
      if (res.status !== '00000') {
        res.message && console.warn(res.message || res.error);
      }

      if (res.data) {
        this._setChartData(data, res.data);
        this._setFilterLabel(data);
      } else {
        this.noData.set(true);
      }
    })
  }


  private async _setFilterLabel(body: AnalysisChart) {
    const { chartConfig, analysisType } = body;
    const { dt, compareDt, filter, metrics, dimensions } = JSON.parse(chartConfig) as QueryInputVo;

    let dimension: DimensionMenuVo;
    const dimensionList = await lastValueFrom(this.apiService.fetchDimension('menu-dimension').pipe(map(res => res.data)));

    if (Array.isArray(dimensions) && dimensions.length > 1) {
      dimensions.shift();
      dimension  = dimensionList.find(item => item.extendName === dimensions[0].extendName);
    }

    const isArea        = ['city_bio_region', 'is_top20_city', 'city_name', 'province_name'].includes(dimensions[0].extendName);
    const dtStr         = `当前期=${Time.format(dt.startTime, dt.endTime) ?? `${dt.startTime}~${dt.endTime}`}`;
    const compareStr    = compareDt ? `对比期=${CompareTime.format(compareDt.startTime, compareDt.endTime) ?? `${compareDt.startTime}~${compareDt.endTime}`}` : '';
    const filterStr     = filter.items.length > 0 ? `维度值=${filter.items.map(item => item.value[0].value).join(', ')}` : '';
    const metricsStr    = `指标=${metrics.map(item => item.aliasName).join(', ')}`;
    const dimensionStr  = isArea ? '构成维度=区域' : dimension ? `构成维度=${dimension.aliasName}` : '';
    const letters       = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');
    const labelArr      = [dtStr, compareStr];
    const comparison = () => {
      return metrics.map((metric, index) => {
        const { aliasName, filter } = metric;
        const values = filter.items.map(items => {
          return items.value.map(item => {
            return item.value;
          })
        })
    
        const labels = [aliasName, ...values].flat(1).join('-');
        return `${letters[index]}:${labels}`;
      })
    }

    const comparisonStr = isNotEmpty(comparison) ? `对比项=${comparison().join(', ')}` : '';

    switch (analysisType) {
      case 1:
        labelArr.push(filterStr);
        labelArr.push(metricsStr);
        break;
      case 2:
        labelArr.push(dimensionStr);
        labelArr.push(filterStr);
        labelArr.push(metricsStr);
        break;
      case 3:
        labelArr.push(comparisonStr);
        break;
    }

    this.filterLabel.set(labelArr.filter(isNotEmpty).join('<br />'));
    this.filterLabelStr.set(labelArr.filter(isNotEmpty).join(' & '));
  }


  private _updateSortFn() {
    const orderType = this.orderType();
      const orderBy = this.orderBy();

      if (this.chart) {
        if (orderType === null) {
          this.chart.sortFn = sortCategoriesFn3('series.name');
        } else {
          this.chart.sortFn = (a, b) => {
            if (orderBy === 'asc') {
              return a.y - b.y;
            } else if (orderBy === 'desc') {
              return b.y - a.y;
            }

            return 0;
          };
        }
      }
  }

  private _setChartData(body: AnalysisChart, result: QueryOutputVo) {
    const { chartShowConfig, chartConfig } = body;
    const { dtType } = JSON.parse(chartConfig) as QueryInputVo;

    const hasDateCompare = result.compareData !== null;
    const legendItemClick = LegendItemClickHandler(this.legendControlService);

    if (chartShowConfig) {
      let { chartType, showType, orderType, orderBy } = JSON.parse(chartShowConfig) || {};
      
      // 注释之前：因为一些误操作原因导致构成分析切换到趋势分析时没有讲showType重置，导致最终图表无法渲染的问题
      // 注释原因：构成分析时，选择只看数值后，占比也显示
      // if (chartType === 'line') {
      //   showType = null;
      // }

      chartType && this.chartType.set(chartType);
      showType && this.legendControlService.setShowType(showType);
      orderType && this.orderType.set(orderType);
      orderBy && this.orderBy.set(orderBy);
    }

    try {
      if (body.analysisType === 1 || body.analysisType === 3) {
        if (hasDateCompare) {
          this.chart = new MultipleXAxis(result, dtType);
        } else {
          this.chart = new BasicLine(result, dtType);
        }
        this.chart.plotOptions.series.events = { legendItemClick };
      }
      else {
        if (hasDateCompare) {
          this.chart = new BarStackMultipleXAxis(result, this.chartType(), dtType);
        } else {
          this.chart = new BarStack(result, this.chartType(), dtType);
        }
  
        this.chart.plotOptions = {
          series: {
            events: { legendItemClick }
          }
        };
      }

      this.option.set(this.chart?.getOption() || null);
      this._updateSortFn();
    } catch (error: any) {
      console.log(error);
      this.errorMessage.set(error);
    }
  }


  download() {
    const { chartConfig } = this.data();
    const body = JSON.parse(chartConfig) as QueryInputVo;

    // trace(`埋点上报: 自助分析 -> 下载按钮点击`);
    this.queryEngineApiService.exportToExcel(body).subscribe(() => {
      // 下载埋点
      // this.buriedPointService.addStat('dida_dpm_caerus_Selfhelp_download_click', {
      //   source: body,
      //   has_date_compare: this.formService.hasDateCompare(),
      //   source_type: this.sourceType(),
      //   dimensions: body.dimensions,
      //   filters: body.filter,
      //   metrics: body.metrics,
      //   compareDt: body.compareDt,
      //   dt: body.dt,
      // });
    })
  }


  @After(windowResize)
  enterFullScreen() {
    if (this.isFullScreen()) return;
    
    this.message.info('按ESC退出全屏');
    this.modalService.open(this.fullScreenContainerTpl(), this.viewContainer);
    this.isFullScreen.set(true);
  }
  
  
  @After(windowResize)
  exitFullScreen() {
    if (this.isFullScreen()) {
      this.isFullScreen.set(false);
      this.modalService.close();
    }
  }


  @Confirmed('确定删除吗?')
  handleDelete(id: number = this.data().id) {
    this.apiService.removeAnalysisChart([id]).subscribe(res => {
      if (res.status !== '00000') {
        this.message.error(res.error);
      }

      if (res.status === '00000') {
        res.data ? this.message.success('删除成功') : this.message.error('删除失败');
        this.remove.emit();
      }
    })
  }


  handleChartInfoEdit() {
    this.modal.create({
      nzTitle: '编辑图表',
      nzContent: ChartEditorComponent,
      nzWidth: 600,
      nzData: this.data(),
      nzOnOk: async ({valid, value}) => {
        if (valid) {
          const request$ = this.apiService.saveAnalysisChart(value).pipe(
            map((res) => {
              if (res.status !== '00000') {
                this.message.error(res.error || res.message);
              }
              if (res.status === '00000') {
                this.message.success('保存成功');
                this.parent.chartResource.reload();
                return true;
              }
              return false;
            })
          );

          return lastValueFrom(request$);
        }
        
        return false
      }
    })
  }

  toggleSelect() {
    if (this.deleteMode()) {
      this.onSelect.emit();
    }
  }
  
}
