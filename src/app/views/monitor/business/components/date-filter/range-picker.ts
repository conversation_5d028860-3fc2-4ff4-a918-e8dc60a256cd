import { DatePipe, KeyValuePipe } from '@angular/common';
import {
  ControlValueAccessor,
  FormsModule,
  NG_VALUE_ACCESSOR,
} from '@angular/forms';
import {
  AfterViewInit,
  booleanAttribute,
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  forwardRef,
  inject,
  input,
  signal,
  viewChild,
} from '@angular/core';
import { CdkConnectedOverlay, OverlayModule } from '@angular/cdk/overlay';
import {
  differenceInCalendarDays,
  differenceInCalendarMonths,
  addMonths,
  addDays,
} from 'date-fns';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import { debounceTime } from 'rxjs';

import { CompareTime, Time } from '@common/class';
import { QueryEngineFormService } from '@common/service/query-engine';
import { EventInterceptor } from '@common/function';
import {
  IconCalendarComponent,
  IconChevronLeftComponent,
  IconChevronRightComponent,
} from '@shared/modules/icons';
import { BaseOverlayDirective } from '@common/directive';
import { Before } from '@common/decorator';

@Component({
  selector: 'range-picker',
  template: `
    <div
      cdkOverlayOrigin
      class="flex items-center h-full px-2.5 py-1.5 border border-neutral-300/90 rounded-xs text-xs text-neutral-700"
      (click)="toggle($event)"
    >
      <CalendarIcon class="text-base" />
      @if (dateLabel()) {
        <span class="bg-primary rounded-full px-2 py-0.5 text-white scale-85">{{ dateLabel() }}</span>
      }
      
      <div class="flex-1 min-w-0 truncate space-x-0.5">
        @for (item of rangeDate(); track $index) {
          @switch (mode()) {
            @case ('week')  { <span>{{ item | date : 'YYYY第ww周' }}</span> } 
            @case ('month') { <span>{{ item | date : 'yyyy-MM' }}</span> } 
            @default        { <span>{{ item | date : 'yyyy-MM-dd' }}</span> }
          } 
          @if ($first && item !== null) {
            <span>至</span>
          }
        } @empty {
          <span>{{ placeholder() }}</span>
        }
      </div>

      @if (mode() === 'date' && rangeDate() !== null) {
        <ChevronLeftIcon iconBtn class="sm" nz-tooltip="向前平移一天" (click)="prevDate($event)" />
        <ChevronRightIcon iconBtn class="sm" nz-tooltip="向后平移一天" (click)="nextDate($event)" [class.disabled]="disabled()" />
      }
    </div>

    <ng-template
      #overlay="cdkConnectedOverlay"
      cdkConnectedOverlay
      [cdkConnectedOverlayPanelClass]="'custom-range-picker-overlay'"
      [cdkConnectedOverlayHasBackdrop]="false"
      [cdkConnectedOverlayOrigin]="cdkOverlayOrigin"
      [cdkConnectedOverlayPositions]="listOfPositions"
      [cdkConnectedOverlayScrollStrategy]="scrollStrategy"
      [cdkConnectedOverlayOpen]="visible()"
      (positionChange)="onPositionChange($event)"
      (overlayOutsideClick)="handleOutside($event)"
      (detach)="close()"
    >
      <div class="flex flex-col bg-white rounded-sm shadow-1">
        <div
          class="relative z-20 flex items-stretch divide-x divide-[#f0f0f0] border-b border-b-[#f0f0f0] "
        >
          @if (rangeMap() && rangeMap()?.size > 0) {
          <aside class="menu-wrapper-column-2" [class.dt]="mode() === 'date'">
            @for (item of (rangeMap() | keyvalue : originalOrder); track $index)
            {
            <div class="menu-item" (click)="onRangeDateChange(item.value)">
              <span>{{ item.key }}</span>
            </div>
            }
          </aside>
          }

          <section class="flex-1 min-w-0">
            @if (mode() === 'month') {
            <nz-range-picker
              nzInline
              [nzMode]="mode()"
              [nzDisabledDate]="disabledMonth"
              [(ngModel)]="rangeDate"
              (ngModelChange)="onRangeDateChange($event)"
            />
            } @else {
            <nz-range-picker
              nzInline
              [nzMode]="mode()"
              [nzDisabledDate]="disabledDate"
              [(ngModel)]="rangeDate"
              (ngModelChange)="onRangeDateChange($event)"
            />
            }
          </section>
        </div>

        <div class="flex justify-end py-2 px-4">
          <button nz-button nzType="primary" nzSize="small" (click)="close()">
            确定
          </button>
        </div>
      </div>
    </ng-template>
  `,
  styleUrl: './range-picker.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    OverlayModule,
    NzDatePickerModule,
    NzButtonComponent,
    NzToolTipModule,
    IconCalendarComponent,
    IconChevronRightComponent,
    IconChevronLeftComponent,
    DatePipe,
    KeyValuePipe,
  ],
  providers: [
    DatePipe,
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => RangePicker),
      multi: true,
    },
  ],
})
export class RangePicker
  extends BaseOverlayDirective
  implements ControlValueAccessor
{
  readonly datePipe = inject(DatePipe);
  readonly formService = inject(QueryEngineFormService);
  readonly destroyRef = inject(DestroyRef);

  mode = input<'date' | 'week' | 'month'>(null);
  // isCompare = input(false, { transform: booleanAttribute });
  rangeMap = input<Map<string, string>>();
  placeholder = input('');

  connectedOverlay = viewChild(CdkConnectedOverlay);

  today = new Date();
  yesterday = signal(addDays(new Date(), -1));
  rangeDate = signal<Date[]>([]);
  disabled = computed(() => {
    if (this.rangeDate()) {
      const [, end] = this.rangeDate();
      const yesterdayStr = new Date(this.yesterday()).toLocaleDateString();
      const endStr = new Date(end).toLocaleDateString();

      return yesterdayStr === endStr;
    }

    return false;
  });

  originValue = signal<string | Date[]>(null);
  dateLabel = computed(() => {
    const value = this.originValue();

    if (Array.isArray(value)) {
      return '自定义日期';
    } else if (typeof value === 'string') {
      const [from, to] = value.split(',');

      return new Time(from, to).toLocalString() ?? '自定义日期';
    }

    return null;
  });

  change = (_: string | Date[]) => {};
  registerOnTouched(fn: any) {}
  registerOnChange(fn: any) {
    this.change = fn;
  }

  writeValue(value: string) {
    if (value === null) {
      this.rangeDate.set([null, null]);
    } else {
      const [baseStart, baseEnd] = Time.getDate(this.mode(), value);
      this.rangeDate.set([baseStart, baseEnd]);
    }

    this.originValue.set(value);
  }

  disabledDate = (current: Date): boolean => {
    // Can not select days before today and today
    return differenceInCalendarDays(current, this.today) > -1;
  };

  disabledMonth = (current: Date): boolean => {
    // Can not select days before today and today
    const startDateForMonth = new Date().setDate(1);
    const dateRight = addMonths(startDateForMonth, 1);

    return differenceInCalendarMonths(current, dateRight) > -1;
  };

  originalOrder = (): number => {
    return 0;
  };

  onRangeDateChange(value: string | Date[]) {
    this._calculateBaseDate(value);
  }

  private _calculateBaseDate(value: string | Date[]) {
    const [start, end] = Time.getDate(this.mode(), value);
    const startDate = Math.min(start.getTime(), end.getTime());
    const endDate = Math.max(end.getTime(), start.getTime());

    this.rangeDate.set([new Date(startDate), new Date(endDate)]);
    this.originValue.set(value);

    if (typeof value === 'string') {
      this.change(value);
    } else {
      switch (this.mode()) {
        case 'date':
          return this.change(
            `${this.datePipe.transform(
              startDate,
              'yyyy-MM-dd'
            )},${this.datePipe.transform(endDate, 'yyyy-MM-dd')}`
          );
        case 'week':
          return this.change(
            `${this.datePipe.transform(
              startDate,
              'YYYY-ww'
            )},${this.datePipe.transform(endDate, 'YYYY-ww')}`
          );
        case 'month':
          return this.change(
            `${this.datePipe.transform(
              startDate,
              'yyyy-MM'
            )},${this.datePipe.transform(endDate, 'yyyy-MM')}`
          );
      }
    }
  }

  override open() {
    this.visible.set(true);

    setTimeout(() => {
      const dom =
        this.connectedOverlay().overlayRef.hostElement?.parentElement
          ?.classList;

      dom?.add('z-999!');
      dom?.remove('z-50!');
    });
  }

  @Before(EventInterceptor)
  prevDate(event: MouseEvent) {
    const [start, end] = this.rangeDate();
    const newStart = addDays(new Date(start), -1);
    const newEnd = addDays(new Date(end), -1);
    const from = this.datePipe.transform(newStart, 'yyyy-MM-dd');
    const to = this.datePipe.transform(newEnd, 'yyyy-MM-dd');
    const value = `${from},${to}`;

    this.rangeDate.set([newStart, newEnd]);
    this.originValue.set(value);
    this.change(value);
  }

  @Before(EventInterceptor)
  nextDate(event: MouseEvent) {
    const [start, end] = this.rangeDate();
    const newStart = addDays(new Date(start), 1);
    const newEnd = addDays(new Date(end), 1);
    const from = this.datePipe.transform(newStart, 'yyyy-MM-dd');
    const to = this.datePipe.transform(newEnd, 'yyyy-MM-dd');
    const value = `${from},${to}`;

    this.rangeDate.set([newStart, newEnd]);
    this.originValue.set(value);
    this.change(value);
  }
}
