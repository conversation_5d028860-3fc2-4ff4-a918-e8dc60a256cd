import { DatePipe } from '@angular/common';
import { FormGroup, FormsModule } from '@angular/forms';
import { AfterViewInit, ChangeDetectionStrategy, Component, DestroyRef, inject, input, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { startWith } from 'rxjs';

import { QueryDt } from '@common/service/query-engine';
import { getDateFromYearWeek, getDateFromYearWeekV2 } from '@common/function';
import { RangePickerComponent } from '../range-picker';


@Component({
  selector: 'app-week-picker',
  template: `
    <app-range-picker class="block w-52 h-8" [ngModel]="value()" (ngModelChange)="handleValueChange($event)" mode="week" [rangeMap]="rangeMap()" />
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    RangePickerComponent,
  ],
  providers: [
    DatePipe,
  ]
})
export class WeekPickerComponent implements AfterViewInit {

  datePipe = inject(DatePipe);
  destroyRef = inject(DestroyRef);

  rangeMap = input<Map<string, Date[]>>();
  form = input.required<any>();
  value = signal<Date[]>(null);

  // 注释原因：
  // 使用effect时如果监听form同时监听value，会导赋值致死循环
  // 则只能监听formChange一次，所以导致更高级的功能无法实现
  // 目前改为非双向绑定value，通过ngModelChange来设置value
  // 解决了赋值死循环的问题
  // constructor() {
  //   effect(() => {
  //     if (this.value()) {
  //       const [startTime, endTime] = this.value().map(value => {
  //         return this.datePipe.transform(value, 'YYYY-ww');
  //       })
        
  //       this.form().get('startTime').patchValue(startTime);
  //       this.form().get('endTime').patchValue(endTime);
  //     }
  //   })
  // }


  ngAfterViewInit(): void {
    (<FormGroup<QueryDt>>this.form()).valueChanges.pipe(
      startWith((<FormGroup<QueryDt>>this.form()).getRawValue()),
      takeUntilDestroyed(this.destroyRef),
    ).subscribe(value => {
      if (value) {
        const { startTime, endTime } = value;

        if (startTime && endTime) {
          this.value.set([
            getDateFromYearWeekV2(startTime), 
            getDateFromYearWeekV2(endTime)
          ]);

          console.log({ startTime, endTime });
          
          // debugger
        }
      }
    })
  }


  handleValueChange(value: Date[]) {
    // console.log('[handleValueChange]', value);
    const [startTime, endTime] = value.map(value => this.datePipe.transform(value, 'YYYY-ww'));
    
    this.form().get('startTime').patchValue(startTime);
    this.form().get('endTime').patchValue(endTime);
  }

}
