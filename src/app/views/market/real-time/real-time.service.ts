import { Injectable, signal } from '@angular/core';
import { toObservable } from '@angular/core/rxjs-interop';
import { BehaviorSubject, interval, merge, Subject, switchMap, takeUntil } from 'rxjs';
import { Throttle } from '@common/decorator';

export interface RealTimeMetric {
  extendName: string,
  aliasName: string;
  showName: string,
  bizExpression: string,
  checked: boolean,
  key: string;

  value: string;
  diff: {
    value: string;
    ratio: string;
  },
  dt: {
    COMPARE_VALUE: string;
    DIFF: string;
    DIFF_RATIO: string;
  };
  yw: {
    COMPARE_VALUE: string;
    DIFF: string;
    DIFF_RATIO: string;
  };
}

interface RealTimeDimension {
  showName: string,
  extendName: string,
  conditions?: Array<{
    dt: string,
    extendName: string,
  }>
}

export interface RealTimeConfig {
  realTimeArea: {
    filter: {
      [key: string]: {
        name: string,
        extendName: string,
        values: Array<{
          value: string,
          key: string,
          relValue?: string,
        }>
      }
    },
    dimensions: RealTimeDimension[],
    name: string,
    minuteExtendName: string;
    metrics: RealTimeMetric[]
  },
  overview: {
    filter: {
      [key: string]: {
        name: string,
        extendName: string,
        values: Array<{
          value: string,
          key: string,
          relValue?: string,
        }>
      }
    },
    name: string,
    accumulateData: {
      name: string,
      minuteExtendName: string;
      metrics: RealTimeMetric[]
    },
    realTimeData: {
      name: string,
      metrics: RealTimeMetric[],
      dimensions: RealTimeDimension[],
    }
  }
}

@Injectable()
export class RealTimeService {

  public readonly minute = 5;
  
  private _source = new BehaviorSubject<void>(null);
  private readonly _period = this.minute * 60000;

  public config = signal<RealTimeConfig>(null);
  public config$ = toObservable(this.config);
  public deadline = signal<string>(null);
  public deadline$ = toObservable(this.deadline);

  
  public stop$ = new Subject<void>();
  public start$ = new BehaviorSubject<void>(null);
  public source$ = this._source.asObservable();
  public interval$ = merge(
    this.start$.pipe(
      switchMap(() => interval(this._period).pipe(
        takeUntil(this.stop$)
      )
    ))
  );

  query() {
    this._source.next();
  }

  stop() {
    this.stop$.next();  // 发出停止信号
  }

  restart() {
    this.start$.next();
  }

  @Throttle(300)
  restore() {
    this.stop$.next();  // 发出停止信号
    this.start$.next();
  }

  updateMetricsValue(obj: { [key: string]: string }) {
    this.config.update(config => {
      const { metrics } = config.overview.accumulateData;
      
      config.overview.accumulateData.metrics = metrics.map(item => {
        const { extendName } = item;
        
        item.value = obj[extendName] || null;
        item.diff = {
          value: obj[extendName + '_DIFF'] || null,
          ratio: obj[extendName + '_DIFF_RATIO'] || null,
        };

        item.dt = {
          COMPARE_VALUE: obj[extendName + ':dt_COMPARE_VALUE'] || null,
          DIFF: obj[extendName + ':dt_DIFF'] || null,
          DIFF_RATIO: obj[extendName + ':dt_DIFF_RATIO'] || null,
        };

        item.yw = {
          COMPARE_VALUE: obj[extendName + ':yw_COMPARE_VALUE'] || null,
          DIFF: obj[extendName + ':yw_DIFF'] || null,
          DIFF_RATIO: obj[extendName + ':yw_DIFF_RATIO'] || null,
        };

        return item;
      })

      return config;
    })
  }

  updateMetricsCheckedState(keys: string[], state: boolean) {
    this.config.update(config => {
      const { metrics } = config.overview.realTimeData;

      config.overview.realTimeData.metrics = metrics.map(item => {
        if (keys.includes(item.extendName)) {
          item.checked = state;
        }
        return item;
      })

      return config;
    });
  }

}
