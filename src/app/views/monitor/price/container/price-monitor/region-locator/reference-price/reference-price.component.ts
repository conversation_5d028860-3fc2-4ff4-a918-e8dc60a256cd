import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  inject,
  QueryList,
  signal,
  ViewChild,
} from '@angular/core'
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop'
import { FormsModule } from '@angular/forms'
import { CaerusApiService } from '@api/caerus'
import { QueryEngineApiService } from '@api/query-engine'
import { PAGE_NAME } from '@common/directive'
import { isNotNull } from '@common/function'
import { BuriedPointService } from '@common/service'
import { QueryEngineFormService } from '@common/service/query-engine'
import { RadioModule } from '@shared/modules/headless'
import { CityPickerComponent, SubFilterComponent } from '@views/monitor/price/components'
import { ReplacePipe } from '@views/monitor/price/pipes/replace'
import { PriceService } from '@views/monitor/price/price.service'
import { find, flattenDeep } from 'lodash'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { NzDropdownMenuComponent } from 'ng-zorro-antd/dropdown'
import { NzIconModule } from 'ng-zorro-antd/icon'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { NzTableModule } from 'ng-zorro-antd/table'
import { NzTagModule } from 'ng-zorro-antd/tag'
import { combineLatest, finalize, debounceTime, startWith } from 'rxjs'

@Component({
  selector: 'app-reference-price',
  imports: [
    RadioModule,
    FormsModule,
    CityPickerComponent,
    NzTableModule,
    NzTagModule,
    NzButtonModule,
    NzPopoverModule,
    NzDropdownMenuComponent,
    SubFilterComponent,
    NzIconModule,
    ReplacePipe,
  ],
  templateUrl: './reference-price.component.html',
  styleUrl: './reference-price.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReferencePriceComponent implements AfterViewInit {
  @ViewChild('menu') menuRefs!: QueryList<NzDropdownMenuComponent>
  readonly apiService = inject(CaerusApiService)
  readonly formService = inject(QueryEngineFormService)
  readonly queryService = inject(QueryEngineApiService)
  readonly destroyRef = inject(DestroyRef)
  readonly buriedPointService = inject(BuriedPointService)

  priceService = inject(PriceService)
  menus = new Map<number, NzDropdownMenuComponent>()
  readonly labelClass = 'inline-flex items-center font-bold leading-0 whitespace-nowrap'
  page_name = inject(PAGE_NAME)

  city_name = signal([])
  price_type_list = signal([])
  metrics = signal([])
  price_type = signal(null)
  loading = signal(false)
  inner_cardrate_dis = signal([])
  metrics$ = toObservable(this.metrics)
  inner_cardrate_dis$ = toObservable(this.inner_cardrate_dis)
  errorMessage = signal(null)
  dt = signal(null)
  tbData = signal([])
  checkedCity = signal([])
  citys = signal([])
  popovercontent = signal({})
  diffValuesMap = signal(new Map<string, any>())

  // 获取差异值
  getDiffValue(key: string): any {
    return this.diffValuesMap().get(key)
  }

  // 设置差异值
  setDiffValue(key: string, value: any): void {
    const newMap = new Map(this.diffValuesMap())
    newMap.set(key, value)
    this.diffValuesMap.set(newMap)
    console.log('key', this.diffValuesMap())
    this.getTableData()
  }

  color = [
    'bg-[rgba(55,127,127,0.1)]!',
    'bg-[rgba(64,149,229,0.1)]!',
    'bg-[rgba(127,131,247,0.1)]!',
    'bg-[rgba(217,154,129,0.1)]!',
    'bg-[rgba(147,194,45,0.1)]!',
    'bg-[rgba(55,127,127,0.1)]!',
    'bg-[rgba(64,149,229,0.1)]!',
    'bg-[rgba(127,131,247,0.1)]!',
    'bg-[rgba(217,154,129,0.1)]!',
    'bg-[rgba(147,194,45,0.1)]!',
    'bg-[rgba(194,45,164,0.1)]!',
  ]

  ngAfterViewInit(): void {
    this.menuRefs?.forEach((menu, index) => {
      this.menus.set(index, menu)
    })
    this.fetchDimension()
    this.fetchMetrics()
    this.fetchConfig()
    this._subscribeChange()
  }

  renderNumberText(num, multiply100 = false) {
    if (num === null || num === undefined) {
      return {
        text: null,
        color: 'text-black',
      }
    }
    if (Number(num) < 0) {
      return {
        text: multiply100 ? (Number(num) * 100).toFixed(2) : Number(num).toFixed(2),
        color: 'text-green-500',
      }
    }
    if (Number(num) > 0) {
      return {
        text: multiply100 ? `+${(Number(num) * 100).toFixed(2)}` : `+${Number(num).toFixed(2)}`,
        color: 'text-red-500',
      }
    }
    return {
      text: Number(num).toFixed(2),
      color: 'text-black',
    }
  }

  private _subscribeChange() {
    combineLatest([
      this.formService.form.valueChanges.pipe(startWith(this.formService.form.value)),
      this.priceService.competitionFilter$,
    ])
      .pipe(startWith([]), debounceTime(100), takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.getTableData()
      })
  }

  fetchConfig() {
    combineLatest([this.metrics$, this.inner_cardrate_dis$])
      .pipe(debounceTime(100), takeUntilDestroyed(this.destroyRef))
      .subscribe(([a, b]) => {
        if (a.length !== 0 && b.length !== 0) {
          this.getTableData()
        }
      })
  }

  fetchDimension() {
    this.apiService.fetchDimensionConfig('price_monitor_dimension').subscribe(res => {
      if (res.data) {
        // console.log('price-config', res.data)
        const lists = find(res.data['price_base'], ['extendName', 'price_type'])?.values
        const _list = find(res.data['price_base'], ['extendName', 'inner_cardrate_dis'])?.values
        this.price_type_list.set(lists)
        this.price_type.set(lists[1])
        _list.forEach(l => {
          l.diffSortFn = (a, b) =>
            a[`dida_conrtar_cardrate_pricediff_${l.key}`] - b[`dida_conrtar_cardrate_pricediff_${l.key}`]
          l.dida_cardrate_amt_sort = (a, b) =>
            a[`dida_mean_cardrate_amt_${l.key}`] - b[`dida_mean_cardrate_amt_${l.key}`]
          l.dida_cardrate_amt_diff_sort = (a, b) =>
            a[`dida_mean_cardrate_amt_${l.key}:dt_DIFF`] - b[`dida_mean_cardrate_amt_${l.key}:dt_DIFF`]
          l.conrtar_cardrate_amt_sort = (a, b) =>
            a[`conrtar_mean_cardrate_amt_${l.key}`] - b[`conrtar_mean_cardrate_amt_${l.key}`]
          l.conrtar_cardrate_amt_diff_sort = (a, b) =>
            a[`conrtar_mean_cardrate_amt_${l.key}:dt_DIFF`] - b[`conrtar_mean_cardrate_amt_${l.key}:dt_DIFF`]
        })
        this.inner_cardrate_dis.set(_list)
        this.citys.set(find(res.data['price_base'], ['extendName', 'city_name'])?.values)
      }
    })
  }

  fetchMetrics() {
    this.apiService.fetchMetricsConfig('price_monitor_metrics_v3').subscribe(res => {
      if (res.data) {
        console.log('metrics', res.data['table_reference_price'])
        this.metrics.set(res.data['table_reference_price']?.subMetric)
        let obj = {}

        this.metrics().forEach(m => {
          obj[m.extendName] = {
            aliasName: m.aliasName,
            bizExpression: m.bizExpression,
          }
        })
        this.popovercontent.set(obj)
      } else {
        this.metrics.set([])
      }
    })
  }

  getTableData() {
    this.checkedCity.set([])
    const body = this.formService.value()
    body.dt = {
      startTime: body.dt.endTime,
      endTime: body.dt.endTime,
    }

    body.dimensions = [
      {
        id: null,
        extendName: 'dt',
        predefineCompareType: ['dt'],
      },
      {
        id: null,
        extendName: 'city_name',
      },
    ]
    body.queryType = 'compare'
    body.scene = 7
    body.dataFillConfig = {
      open: 1,
      fillRangeType: 1,
      fillValue: 1,
    }
    const metricsFilter = []

    if (this.priceService.competitionName() === 'GD') {
      metricsFilter.push({
        conditionType: 2,
        condition: '!=',
        id: null,
        extendName: 'dida_mean_cardrate_amt_10',
        value: [
          {
            key: 'null',
            value: 'null',
          },
        ],
        valueType: 'metrics',
      })
    }

    for (let [key, value] of this.diffValuesMap()) {
      metricsFilter.push(value)
    }

    body.metricsFilter = {
      items: metricsFilter.filter(isNotNull),
    }

    const items = [
      {
        conditionType: 2,
        condition: '=',
        id: null,
        extendName: 'c_ord_type',
        value: [
          {
            key: '0',
            value: '市内',
          },
        ],
        valueType: null,
      },
    ]

    if (this.city_name().length !== 0) {
      items.push({
        conditionType: 2,
        condition: 'in',
        id: null,
        extendName: 'city_name',
        value: this.city_name(),
        valueType: null,
      })
    }

    body.filter = {
      items,
      type: null,
    }

    const filter = this.inner_cardrate_dis().map(dis => {
      return [
        {
          conditionType: 2,
          condition: '=',
          id: null,
          extendName: 'inner_cardrate_dis',
          value: [
            {
              key: dis.key,
              value: dis.value,
            },
          ],
          valueType: null,
        },
        {
          conditionType: 2,
          condition: '=',
          id: null,
          extendName: 'price_type',
          value: [
            {
              key: this.price_type().key,
              value: this.price_type().value,
            },
          ],
          valueType: null,
        },
      ]
    })

    const arr = filter.map(f => {
      return this.metrics().map(m => {
        return {
          userDefExtendName: `${m.extendName}_${f[0].value[0].key}`,
          extendName: m.extendName,
          filter: {
            items: f.concat(this.priceService.competitionFilter()),
            type: null,
          },
        }
      })
    })

    body.metrics = flattenDeep(arr)
    body.metrics.push({
      extendName: 'c_finish_ord_cnt',
      aliasName: '车主活跃节点率',
      type: 3,
      bizExpression: '口径文案',
      tagName: 'order',
      display: 1,
      tagOrder: 0,
      displayOrder: 2,
      recommend: 1,
      userDefExtendName: 'c_finish_ord_cnt',
      filter: {
        items: [
          {
            conditionType: 2,
            condition: '=',
            id: null,
            extendName: 'c_firlev_channel',
            value: [
              {
                key: '自有渠道',
                value: '自有渠道',
              },
            ],
            valueType: null,
          },
        ],
      },
    })
    body.orders = [
      {
        extendName: 'c_finish_ord_cnt',
        type: 'desc',
      },
    ]

    // body.userDefExpressions = this.inner_cardrate_dis().map(inner => {
    //   return {
    //     userDefAliasName: `diff_${inner.key}`,
    //     userDefExtendName: `diff_${inner.key}`,
    //     expression: `dida_mean_cardrate_amt_${inner.key}-conrtar_mean_cardrate_amt_${inner.key}`,
    //   }
    // })
    this.loading.set(true)
    this.queryService
      .search(body, 'reference-price-table')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        if (res.data) {
          console.log('table-res', res.data)
          this.tbData.set(res.data.data)
        } else {
          this.tbData.set([])
          this.errorMessage.set(res.message)
        }
      })
  }

  closeTag(name) {
    const arr = this.checkedCity().filter(c => c !== name)
    this.checkedCity.set(arr)
  }

  onItemChecked(name, checked) {
    if (checked) {
      this.checkedCity().push(name)
      this.checkedCity.set(this.checkedCity())
    } else {
      const arr = this.checkedCity().filter(c => c !== name)
      this.checkedCity.set(arr)
    }
  }

  compare(city_name) {
    const cityName = [city_name].map(c => {
      return find(this.citys(), ['value', c])
    })
    const orderType = {
      key: '0',
      value: '市内',
      showValue: '市内',
      extendName: 'c_ord_type',
      showGroup: null,
      defaultShow: 0,
      showOrder: -1,
    }
    const priceType = this.price_type()
    this.priceService.quickCompare({
      isReferencePrice: true,
      cityName,
      orderType,
      priceType,
      scroll: true,
    })
    this.buriedPointService.addStat('dida_dpm_caerus_Price_Contrast_click', {
      page_name: this.page_name,
      Pricetab_name: '最新刊例价',
    })
  }
}
