<header class="relative flex items-center p-5 px-6">
  <span class="font-black text-base">核心业务维度</span>
</header>

@if (parent.view() === 'finish') {
  <ng-template *ngTemplateOutlet="radioTemplate; context: { $implicit: finishMetrics() }"></ng-template>
}
@else {
  <ng-template *ngTemplateOutlet="radioTemplate; context: { $implicit: bookMetrics() }"></ng-template>
}


<ng-template #radioTemplate let-items>
  <app-carousel>
    <app-radio-group class="flex gap-x-3 p-1" jumpToFocus [(ngModel)]="metric" (ngModelChange)="onMetricsChange($event)" appIntersection (visible)="onVisible(1)">
      @for (item of items; track $index) {
        <app-radio [value]="item" class="radio w-64 min-h-[112px]" activeClass="active" (mouseup)="handlerIndicatorCardClick(item)" nz-popover nzPopoverPlacement="topLeft" [nzPopoverMouseEnterDelay]="0.5" [nzPopoverTitle]="metricsTitleTemplate" [nzPopoverContent]="item?.bizExpression">
          <div class="flex flex-col gap-y-1.5">
            <ng-template #metricsTitleTemplate>{{item?.key}} <span class="text-xs opacity-30 px-1">({{item?.aliasName}})</span></ng-template>
            <div class="truncate text-sm">
              <span class="inline-block scale-95 origin-left">{{item.key}}</span>
            </div>
            <div class="font-semibold text-2xl leading-none">
              {{item?.value === null ? '-' : (item?.value | increment | async | number: '1.0-0')}}
            </div>
            @if (item.diff) {
              <div class="flex flex-col gap-y-0.5 whitespace-nowrap">
                <span class="text-sm">较对比期: </span>
                <span>
                  <value-formatter useColor useMultiplier="false" [value]="item.diff" />
                  <value-formatter useColor useMultiplier="false" [value]="item.ratio" showBracket suffix="%" />
                </span>
              </div>
            }
            <SuccessFillIcon *radioChecked class="absolute right-3 text-blue-600 text-xl" />
          </div>
        </app-radio>
      }
    </app-radio-group>
  </app-carousel>
</ng-template>


<div #metricSelect class="flex flex-col py-5 gap-y-2.5">
  <div class="flex items-center gap-x-1 px-5 leading-none">
    <span class="font-black text-base">选择指标对比</span>
    <span class="explain">说明：实线是当前期数据，虚线是对比期数据</span>
  </div>
</div>

<div class="flex">
  <div class="flex-1 min-w-0" appIntersection (visible)="onVisible(2)">
    <div class="pl-5">
      @if (targetVisible()) {
        <label class="inline-flex items-center gap-x-1 text-xs pr-2">
          <nz-switch class="leading-none" nzSize="small" [(ngModel)]="compareTotal"></nz-switch>
          对比大盘
        </label>
      }
  
      @if (parent.view() === 'finish') {
        @for (item of finishLegends(); track $index) {
          <label nz-checkbox class="ml-0! text-xs!" [(ngModel)]="item.checked" (ngModelChange)="onLegendChange()">{{item.key}}</label>
        }
      }
      @else {
        @for (item of bookLegends(); track $index) {
          <label nz-checkbox class="ml-0! text-xs!" [(ngModel)]="item.checked" (ngModelChange)="onLegendChange()">{{item.key}}</label>
        }
      }
    </div>

    <div class="relative flex items-center justify-center h-100">
      @if (querying1() || querying2()) {
        <div class="absolute inset-0 flex items-center justify-center bg-white/70 transition-opacity">
          <app-line-spin />
        </div>
      }
      @else {
        @if (noData()) {
          <div class="flex flex-col items-center gap-y-3">
            <img src="assets/images/svg/empty.svg">
            <span class="text-slate-500">无数据</span>
          </div>
        }
        @else if (option()) {
          <div class="absolute inset-0">
            <!-- <app-charts [options]="option()" /> -->
            <app-graph [options]="option()" />
          </div>
        }
        @else if (errorMessage()) {
          <span class="text-slate-600 text-xs">{{errorMessage()}}</span>
        }
        @else if (selectedLegends().length === 0) {
          <span class="text-slate-600 text-xs">请选择指标进行查询</span>
        }
      }
    </div>
  </div>

  @if (isNotDauMetric()) {
    <app-distribution class="min-w-1/3 max-w-1/3" appIntersection (visible)="onVisible(3)" />
  }
</div>
