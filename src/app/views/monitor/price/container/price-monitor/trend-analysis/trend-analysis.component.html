<div class="flex flex-col gap-3 pt-4">
  <div class="flex items-center relative p-5 px-6">
    <span class="flex items-center gap-x-1.5 font-black text-base">
      <MapArrowRightIcon />
      趋势分析
    </span>

    <app-radio-group
      [(ngModel)]="type"
      (ngModelChange)="changeType()"
      class="absolute inset-1/2 top-1/2 -translate-y-1/2 -translate-x-1/2 w-60 h-8 flex items-start gap-x-0.5 p-0.5 bg-[#ebebec] rounded-lg"
    >
      <app-radio class="dida-radio-new" activeClass="active" value="published-price">刊例价格</app-radio>
      <app-radio class="dida-radio-new" activeClass="active" value="route-price">路线价格</app-radio>

      <app-radio-thumb [offsetX]="2" class="bg-linear-to-r from-[#465867] to-[#1E2C3A] rounded-md" />
    </app-radio-group>
  </div>

  <div class="flex items-center flex-wrap gap-x-3 gap-y-2">
    <app-date-compare class="-ml-8" [class.pointer-events-none]="querying()" deleteBtn showDtType="false" />
    <div class="flex items-center">
      <label [class]="labelClass">订单类型：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="c_ord_type" (ngModelChange)="changeOrdType()">
        <app-radio class="tag-radio" activeClass="active" value="0">市内</app-radio>
        @if (type() === 'route-price') {
          <app-radio class="tag-radio" activeClass="active" value="1">城际</app-radio>
        }
      </app-radio-group>
    </div>
    @if (changeCitys().length !== 0) {
      <div class="text-xs">
        当前时间周期内，{{ c_ord_type() === '0' ? '市内' : '城际' }}共有
        @for (item of changeCitys(); track item) {
          <span class="text-blue-400 cursor-pointer pr-2" (click)="clickCity(item)">{{ item }},</span>
        }
        等{{ changeCitys().length }}个城市进行了调价实验。
        <span class="text-blue-400 cursor-pointer" (click)="clickDetail()">查看详情</span>
      </div>
    } @else {
      <div>当前时间周期内，没有城市进行了调价实验。</div>
    }
  </div>
  <div class="flex items-center flex-wrap gap-x-10 gap-y-2">
    <div class="flex items-center">
      <label [class]="labelClass">起点城市：</label>
      @if (type() === 'route-price') {
        <app-city-picker [(ngModel)]="city_region" (ngModelChange)="getRheaList(); getChartData(); openPriceChange()" />
      } @else {
        <app-city-picker
          useReferencePrice
          [(ngModel)]="city_name"
          (ngModelChange)="getRheaList(); getChartData(); openPriceChange()"
        />
      }
    </div>

    <div class="flex items-center">
      <label [class]="labelClass">价格范围选择：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="rheaType" (ngModelChange)="changeRheaType()">
        <app-radio
          class="tag-radio"
          activeClass="active"
          value="2"
          nz-tooltip
          nzTooltipTitle="可查看该城市整体的价格水平"
        >
          大盘
        </app-radio>
        <app-radio
          class="tag-radio"
          activeClass="active"
          [disabled]="rheaList().length === 0"
          value="3"
          nz-tooltip
          nzTooltipTitle="可查看该城市某个价格实验期间，控制组、实验组分别的价格"
        >
          仅实验场景
        </app-radio>
      </app-radio-group>
      <!-- <nz-select [(ngModel)]="rheaType" class="w-[180px]" (ngModelChange)="changeRheaType()">
        <nz-option nzLabel="整体价格" nzCustomContent nzValue="2">
          <span nz-tooltip nzTooltipTitle="可查看该城市整体的价格水平">整体价格</span>
        </nz-option>
        <nz-option nzCustomContent [nzDisabled]="rheaList().length === 0" nzLabel="实验价格" nzValue="3">
          <span nz-tooltip nzTooltipTitle="可查看该城市某个价格实验期间，控制组、实验组分别的价格">实验价格</span>
        </nz-option>
      </nz-select> -->
    </div>
    @if (rheaType() === '3') {
      <div class="flex items-center">
        <label [class]="labelClass">实验选择：</label>
        <nz-select [(ngModel)]="rheaValue" class="w-[400px]" (ngModelChange)="changeRhea()">
          @for (item of rheaList(); track item) {
            <nz-option [nzLabel]="item.value" [nzValue]="item.experimentId"></nz-option>
          }
        </nz-select>
      </div>
      <div class="flex items-center">
        <label [class]="labelClass">实验组选择：</label>
        <nz-select
          [(ngModel)]="groupValue"
          nzMode="multiple"
          [nzMaxMultipleCount]="2"
          class="w-[600px]"
          (ngModelChange)="getChartData()"
        >
          @for (item of groupList(); track item) {
            <nz-option [nzLabel]="item.value" [nzValue]="item.experimentGroupId"></nz-option>
          }
        </nz-select>
      </div>
    }
  </div>
  <div class="flex items-center flex-wrap gap-x-10 gap-y-2">
    <div class="flex items-center">
      <label [class]="labelClass">价格类型选择：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="price_type" (ngModelChange)="getChartData()">
        @for (l of price_type_list(); track l) {
          <app-radio class="tag-radio" activeClass="active" [value]="l">{{ l.showValue }}</app-radio>
        }
      </app-radio-group>
    </div>
    <div class="flex items-center">
      <label [class]="labelClass">里程：</label>
      <nz-select
        class="w-50"
        [nzPlaceHolder]="type() === 'route-price' ? '全部' : '请选择'"
        [(ngModel)]="intal"
        nzAllowClear
        (ngModelChange)="getChartData()"
      >
        @for (item of intal_list(); track item) {
          <nz-option [nzLabel]="item.showValue" [nzValue]="item"></nz-option>
        }
      </nz-select>
    </div>
  </div>
  <div class="flex flex-col h-160 shadow-md rounded-sm border border-neutral-100">
    <div class="flex flex-col gap-2 px-4 pt-4">
      <div class="flex items-center gap-6">
        <div class="flex items-center">
          <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
            嘀嗒价格指标：
          </label>
          <app-radio-group class="relative flex gap-1" [(ngModel)]="dida_price" (ngModelChange)="changeDidaPrice()">
            @for (l of dida_price_list(); track l) {
              <ng-template #metricsTitleTemplate>
                {{ l?.aliasName | replace: priceService.competitionName() }}
              </ng-template>
              <ng-template #metricsContentTemplate>
                <div
                  class="leading-normal"
                  [innerHTML]="l?.bizExpression | replace: priceService.competitionName()"
                ></div>
              </ng-template>
              <app-radio
                nz-popover
                [nzPopoverMouseEnterDelay]="0.5"
                [nzPopoverTitle]="metricsTitleTemplate"
                [nzPopoverContent]="metricsContentTemplate"
                class="tag-radio"
                activeClass="active"
                [value]="l.extendName"
              >
                {{ l.aliasName | replace: priceService.competitionName() }}
              </app-radio>
            }
          </app-radio-group>
          <!-- <nz-checkbox-group [(ngModel)]="dida_price" (ngModelChange)="getChartData()">
            @for (l of dida_price_list(); track l) {
              <ng-template #metricsTitleTemplate>
                {{ l?.aliasName | replace: priceService.competitionName() }}
              </ng-template>
              <ng-template #metricsContentTemplate>
                <div
                  class="leading-normal"
                  [innerHTML]="l?.bizExpression | replace: priceService.competitionName()"
                ></div>
              </ng-template>
              <label
                nz-popover
                [nzPopoverMouseEnterDelay]="0.5"
                [nzPopoverTitle]="metricsTitleTemplate"
                [nzPopoverContent]="metricsContentTemplate"
                nz-checkbox
                class="text-xs! ml-0!"
                [nzValue]="l.extendName"
              >
                {{ l.aliasName | replace: priceService.competitionName() }}
              </label>
            }
          </nz-checkbox-group> -->
        </div>
        @if (type() === 'route-price') {
          <div class="flex items-center">
            <label
              nz-tooltip
              nzTooltipTitle="说明：开启后将展示嘀嗒24年3月份的每公里均价，作为基准价"
              class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap"
            >
              是否展示价格参照：
            </label>
            <nz-switch
              [(ngModel)]="dida_benchmark_book_ord_amt"
              nzSize="small"
              (ngModelChange)="getChartData()"
            ></nz-switch>
            <!-- <span class="text-xs text-neutral-400 px-4">
              说明：开启后将展示
              <span class="text-red-500">嘀嗒24年3月份</span>
              的
              <span class="text-red-500">每公里均价</span>
              ，作为
              <span class="text-red-500">基准价</span>
            </span> -->
          </div>
        }
        <div class="flex items-center">
          <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
            是否展示价格调整动作：
          </label>
          <nz-switch [(ngModel)]="showPriceChange" nzSize="small" (ngModelChange)="openPriceChange()"></nz-switch>
          <span class="text-xs text-neutral-400 px-4">
            说明：开启后会展示使
            <span class="text-red-500">嘀嗒价格</span>
            发生
            <span class="text-red-500">变化</span>
            的
            <span class="text-red-500">关键操作</span>
            ，以标签的形式在时间轴上展示，
            <span class="text-red-500">点击</span>
            标签可
            <span class="text-red-500">查看操作详情</span>
          </span>
        </div>
      </div>
      <div class="flex items-center">
        <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
          价格对比指标：
        </label>
        <app-radio-group class="relative flex gap-1" [(ngModel)]="compare_price" (ngModelChange)="getChartData()">
          @for (l of compare_price_list(); track l) {
            <ng-template #metricsTitleTemplate2>
              {{ l?.aliasName | replace: priceService.competitionName() }}
            </ng-template>
            <ng-template #metricsContentTemplate2>
              <div
                class="leading-normal"
                [innerHTML]="l?.bizExpression | replace: priceService.competitionName()"
              ></div>
            </ng-template>
            <app-radio
              nz-popover
              [nzPopoverMouseEnterDelay]="0.5"
              [nzPopoverTitle]="metricsTitleTemplate2"
              [nzPopoverContent]="metricsContentTemplate2"
              class="tag-radio"
              activeClass="active"
              [value]="l.extendName"
              [disabled]="l.tagOrder !== tag_order() && l.tagOrder !== 0"
            >
              {{ l.aliasName | replace: priceService.competitionName() }}
            </app-radio>
          }
        </app-radio-group>
        <!-- <nz-checkbox-group [(ngModel)]="compare_price" (ngModelChange)="getChartData()">
          @for (l of compare_price_list(); track l) {
            <ng-template #metricsTitleTemplate2>
              {{ l?.aliasName | replace: priceService.competitionName() }}
            </ng-template>
            <ng-template #metricsContentTemplate2>
              <div
                class="leading-normal"
                [innerHTML]="l?.bizExpression | replace: priceService.competitionName()"
              ></div>
            </ng-template>
            <label
              nz-popover
              [nzPopoverMouseEnterDelay]="0.5"
              [nzPopoverTitle]="metricsTitleTemplate2"
              [nzPopoverContent]="metricsContentTemplate2"
              nz-checkbox
              class="text-xs! ml-0!"
              [nzValue]="l.extendName"
            >
              {{ l.aliasName | replace: priceService.competitionName() }}
            </label>
          }
        </nz-checkbox-group> -->
      </div>
    </div>

    <div class="flex-1 min-h-0 relative flex items-center justify-center text-xs">
      @if (loading()) {
        <app-line-spin />
      } @else {
        @if (chartValue()) {
          <div class="absolute inset-2">
            <!-- <app-graph [options]="option()" showAvgPlotLines showAnnotations [multiple]="1" /> -->
            <app-grid-multiple
              [value]="chartValue()"
              [markLineValue]="markLineValue()"
              [groupList]="groupList()"
              (click)="clickMarkLine($event)"
            />
          </div>
        } @else if (errorMessage()) {
          <span>{{ errorMessage() }}</span>
        } @else {
          <span></span>
        }
      }
    </div>
  </div>
  <nz-modal
    [(nzVisible)]="isVisible"
    nzTitle=""
    [nzFooter]="null"
    [nzMaskClosable]="false"
    (nzOnCancel)="isVisible.set(false)"
    [nzWidth]="900"
  >
    <ng-container *nzModalContent>
      <div class="flex flex-col gap-2 h-[800px] overflow-y-auto">
        <div class="flex items-center justify-between pt-4">
          <div class="font-bold text-[16px]">周期内调价时间轴</div>
          <div class="flex items-center gap-5">
            <div class="flex items-center">
              <label [class]="labelClass">城市筛选：</label>
              @if (type() === 'route-price') {
                <app-city-picker [(ngModel)]="city" multiple (ngModelChange)="showModal()" />
              } @else {
                <app-city-picker useReferencePrice [(ngModel)]="city" multiple (ngModelChange)="showModal()" />
              }
            </div>
            <div class="flex items-center">
              <label [class]="labelClass">时间范围：</label>
              <nz-range-picker
                [(ngModel)]="date"
                [nzAllowClear]="false"
                (ngModelChange)="clickDetail()"
              ></nz-range-picker>
            </div>
          </div>
        </div>
        <span class="text-xs text-neutral-400">
          说明：弹窗主要展示时间范围内，影响城市价格变动的关键动作；当前主要包括调价实验的开启和关闭，后续会补充CMS的一键全量及基础价调整等信息。
        </span>
        @if (detailLoading()) {
          <nz-spin nzSimple></nz-spin>
        } @else if (!detailData()) {
          <div>暂无日志</div>
        } @else {
          <div class="pt-4">
            <ng-template #dotTemplate>
              <div class="w-[10px] h-[10px] bg-blue-400 rounded-4xl"></div>
            </ng-template>
            <nz-timeline>
              @for (key of Object.keys(detailData()); track key) {
                <nz-timeline-item [nzDot]="dotTemplate">
                  <div class="text-[16px] font-bold">
                    {{ key }}
                  </div>
                </nz-timeline-item>
                @for (ids of Object.keys(detailData()[key]); track ids) {
                  @if (getFilteredItems(key, ids, 1).length !== 0) {
                    <nz-timeline-item>
                      <div class="bg-blue-50 rounded-sm flex flex-col gap-2 py-2 px-4 border-2 border-blue-500 w-[90%]">
                        @for (item of getFilteredItems(key, ids, 1); track item) {
                          @if ($index === 0) {
                            <div class="flex items-center justify-between">
                              <div>
                                <!-- <span class="font-bold">{{ item.opName }}</span> -->
                                {{ item.cityName }}开启了{{ item.ordTypeName }}的
                                <span
                                  nz-tooltip
                                  nzTooltipTitle="点击去Rhea查看实验详情"
                                  class="text-blue-400 cursor-pointer"
                                  (click)="gotoRhea(item.expId)"
                                >
                                  基础价实验
                                </span>
                              </div>
                              <div>
                                <span class="font-bold">动作类型:</span>
                                {{ getAction(key, ids) }}
                              </div>
                              <div class="text-gray-400">{{ item.opTime | date: 'yyyy-MM-dd HH:mm:ss' }}</div>
                            </div>
                          }
                          <div>
                            <span class="text-blue-400">{{ $index + 1 }}.</span>
                            {{ item.minKm }}~{{ item.maxKm }}公里段价格{{ item.priceDiffRate < 0 ? '下调' : '上涨' }}
                            {{ item.priceDiffRate | number: '1.0-2' }}%
                          </div>
                        }
                      </div>
                    </nz-timeline-item>
                  }

                  @for (item of getFilteredItems(key, ids, 4); track item) {
                    <nz-timeline-item>
                      <div
                        class="bg-blue-50 rounded-sm px-4 py-2 border-2 border-blue-500 flex justify-between w-[90%]"
                      >
                        <div>
                          <!-- <span class="font-bold">{{ item.opName }}</span> -->
                          {{ item.cityName }}关闭了{{ item.ordTypeName }}的
                          <span
                            nz-tooltip
                            nzTooltipTitle="点击去Rhea查看实验详情"
                            class="text-blue-400 cursor-pointer"
                            (click)="gotoRhea(item.expId)"
                          >
                            基础价实验
                          </span>
                        </div>
                        <div class="text-gray-400">{{ item.opTime | date: 'yyyy-MM-dd HH:mm:ss' }}</div>
                      </div>
                    </nz-timeline-item>
                  }
                }
              }
            </nz-timeline>
          </div>
        }
      </div>
    </ng-container>
  </nz-modal>
</div>
