import { FormsModule } from '@angular/forms'
import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, OnInit, signal } from '@angular/core'
import { NzTableModule } from 'ng-zorro-antd/table'
import { NzSelectComponent, NzOptionComponent, NzSelectModule } from 'ng-zorro-antd/select'
// import { NzTagComponent } from 'ng-zorro-antd/tag'
import { CaerusApiService } from '@api/caerus'
import { DimensionValueMenuVo } from '@api/caerus/model'
import { CityPickerComponent, SubFilterComponent } from '@views/monitor/price/components'
import { RadioModule } from '@shared/modules/headless'
import { find, uniqueId, unionBy, flattenDeep } from 'lodash'
import { QueryEngineFormService } from '@common/service/query-engine'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { combineLatest, debounceTime, finalize, startWith } from 'rxjs'
import { Before, SwitchMap } from '@common/decorator'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { PriceService } from '@views/monitor/price/price.service'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { NzToolTipModule } from 'ng-zorro-antd/tooltip'
import { ActivatedRoute } from '@angular/router'
import { NzDropdownMenuComponent } from 'ng-zorro-antd/dropdown'
import { NzIconModule } from 'ng-zorro-antd/icon'
import { isNotNull, isNotUndefined, isNotEmpty } from '@common/function'
import { PAGE_NAME } from '@common/directive'
import { BuriedPointService } from '@common/service'

@Component({
  selector: 'app-alert-monitor',
  templateUrl: './alert-monitor.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    NzTableModule,
    NzSelectComponent,
    NzOptionComponent,
    RadioModule,
    CityPickerComponent,
    NzSelectModule,
    NzButtonModule,
    NzPopoverModule,
    NzToolTipModule,
    NzDropdownMenuComponent,
    SubFilterComponent,
    NzIconModule,
  ],
})
export class AlertMonitorComponent implements OnInit {
  readonly apiService = inject(CaerusApiService)
  readonly formService = inject(QueryEngineFormService)
  readonly destroyRef = inject(DestroyRef)
  readonly buriedPointService = inject(BuriedPointService)

  params = signal(null)
  page_name = inject(PAGE_NAME)

  constructor(private route: ActivatedRoute) {
    this.route.queryParamMap.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(paramMap => {
      const monitorId = paramMap.get('monitorId')
      const isDefault = paramMap.get('default')
      // console.log('monitorid', monitorId, isDefault)
      if (monitorId !== null) {
        if (isDefault === 'false') {
          this.current_type.set('customize')
        } else {
          this.current_type.set('default')
        }
        this.params.set({
          monitorId,
          isDefault,
        })
      } else {
        this.params.set(null)
      }
    })
  }
  priceService = inject(PriceService)

  price_type = signal<DimensionValueMenuVo>(null)

  orderTypeOptions = signal<DimensionValueMenuVo[]>([])
  priceTypeOptions = signal<DimensionValueMenuVo[]>([])

  monitor_type = signal(null)
  default_monitor_list = signal<any>([])
  monitor_project = signal(null)
  current_type = signal('default')
  city = signal([])
  c_ord_type = signal('0')
  config = signal(null)
  intal = signal([null])
  treeData = signal<any>([])
  loading = signal(false)
  tbData = signal([])
  checkedCity = signal([])
  metrics = signal([])
  city_region = signal([])
  city_name = signal([])
  headers = signal(null)
  checkedIds = signal([])
  colors = [
    'bg-[rgba(55,127,127,0.1)]!',
    'bg-[rgba(64,149,229,0.1)]!',
    'bg-[rgba(127,131,247,0.1)]!',
    'bg-[rgba(217,154,129,0.1)]!',
    'bg-[rgba(147,194,45,0.1)]!',
    'bg-[rgba(55,127,127,0.1)]!',
    'bg-[rgba(64,149,229,0.1)]!',
    'bg-[rgba(127,131,247,0.1)]!',
    'bg-[rgba(217,154,129,0.1)]!',
    'bg-[rgba(147,194,45,0.1)]!',
    'bg-[rgba(194,45,164,0.1)]!',
  ]

  diffValuesMap = signal(new Map<string, any>())

  // 获取差异值
  getDiffValue(key: string): any {
    const value = this.diffValuesMap().get(key)
    return value
  }

  // 设置差异值
  setDiffValue(key: string, value: any): void {
    const newMap = new Map(this.diffValuesMap())
    newMap.set(key, value)
    this.diffValuesMap.set(newMap)
    // console.log('key', this.diffValuesMap())
    this.getTableData()
  }

  column = computed(() => {
    if (!this.monitor_project() || !this.headers()) {
      return []
    }
    // console.log('this.monitor_project', this.monitor_project())
    const metrics = (this.monitor_project().metricsList || []).map(m => {
      return {
        name: find(this.metrics(), ['extendName', m.extendName])?.aliasName,
        bizExpression: find(this.metrics(), ['extendName', m.extendName])?.bizExpression,
        extendName: m.extendName,
        predefineCompareType: m.predefineCompareType === '' ? null : m.predefineCompareType,
      }
    })
    const arr = []
    metrics.forEach((m, i) => {
      if (m.predefineCompareType) {
        arr.push({
          name: '当前期值',
          extendName: m.extendName,
          rowspan: null,
          nzSortOrder: null,
          nzSortFn: (a, b) => a[m.extendName] - b[m.extendName],
          nzSortDirections: ['ascend', 'descend', null],
          color: this.colors[i],
          dataUnit: this.headers()[m.extendName]?.dataUnit,
        })
        let extendName
        extendName = m.predefineCompareType === 'dt' ? `${m.extendName}:dt_DIFF` : `${m.extendName}:yw_DIFF`
        if (this.headers()[extendName]?.dataUnit !== '%') {
          extendName =
            m.predefineCompareType === 'dt' ? `${m.extendName}:dt_DIFF_RATIO` : `${m.extendName}:yw_DIFF_RATIO`
        }
        arr.push({
          name: m.predefineCompareType === 'dt' ? '日环比' : '周同比',
          extendName,
          rowspan: null,
          nzSortOrder: null,
          nzSortFn: (a, b) => a[extendName] - b[extendName],
          nzSortDirections: ['ascend', 'descend', null],
          color: this.colors[i],
          dataUnit: this.headers()[m.extendName]?.dataUnit === '%' ? 'pp' : '%',
        })
      }
    })
    const extend = metrics.map((m, i) => {
      return {
        name: m.name,
        bizExpression: m.bizExpression,
        popover: true,
        extendName: m.extendName,
        colspan: m.predefineCompareType ? '2' : null,
        rowspan: m.predefineCompareType ? null : '2',
        nzSortOrder: null,
        color: this.colors[i],
        nzSortFn: m.predefineCompareType ? null : (a, b) => a[m.extendName] - b[m.extendName],
        dataUnit: this.headers()?.[m.extendName]?.dataUnit,
        nzSortDirections: m.predefineCompareType ? null : ['ascend', 'descend', null],
        hasPredefineCompareType: m.predefineCompareType
          ? m.predefineCompareType === 'dt'
            ? `${m.extendName}:dt_DIFF`
            : `${m.extendName}:yw_DIFF`
          : null,
      }
    })
    let extendName
    if (this.monitor_type() === 1) {
      extendName = this.c_ord_type() === '0' ? 'inner_miage_intal' : 'inter_miage_intal'
    } else {
      extendName = 'inner_cardrate_dis'
    }
    // console.log('extend', extend)
    if (this.monitor_type() !== 3) {
      return [
        [
          // {
          //   name: '选择洞察目标',
          //   rowspan: '2',
          // },
          {
            name: '起点城市',
            rowspan: '2',
            extendName: this.monitor_type() !== 2 ? 'city_region' : 'city_name',
          },
          {
            name: '订单类型',
            rowspan: '2',
            extendName: 'c_ord_type',
          },
          {
            name: '价格类型',
            rowspan: '2',
            extendName: 'price_type',
          },
          {
            name: this.monitor_type() === 1 ? '公里段' : '公里数',
            rowspan: '2',
            extendName,
          },
          ...extend,
          {
            name: '监控日期',
            rowspan: '2',
            extendName: 'dt',
          },
        ],
        arr,
      ]
    }
    return [
      [
        // {
        //   name: '日环比察目标',
        //   rowspan: '2',
        // },
        {
          name: '起点城市',
          rowspan: '2',
          extendName: this.monitor_type() !== 2 ? 'city_region' : 'city_name',
          fixed: true,
        },
        ...extend,
        {
          name: '监控日期',
          rowspan: '2',
          extendName: 'dt',
        },
      ],
      arr,
    ]
  })

  extendNames = computed(() => {
    const arr = []
    this.column()[0].forEach((item, i) => {
      if (item.extendName) {
        if (!item.hasPredefineCompareType) {
          arr.push({
            extendName: item.extendName,
            dataUnit: this.headers()[item.extendName]?.dataUnit,
            multiply100: this.headers()[item.extendName]?.dataUnit === '%',
          })
        } else {
          arr.push(
            {
              extendName: item.extendName,
              dataUnit: this.headers()[item.extendName]?.dataUnit,
              multiply100: this.headers()[item.extendName]?.dataUnit === '%',
            },
            {
              extendName:
                this.headers()[item.extendName]?.dataUnit === '%'
                  ? item.hasPredefineCompareType
                  : `${item.hasPredefineCompareType}_RATIO`,
              dataUnit: this.headers()[item.extendName]?.dataUnit === '%' ? 'pp' : '%',
              multiply100: this.headers()[item.extendName]?.dataUnit === '%',
            }
          )
        }
      }
    })
    console.log('extendNames', arr)
    return arr
  })

  intal_list = computed(() => {
    if (!this.config()) {
      return []
    }
    if (this.monitor_type() === 1) {
      const extendName = this.c_ord_type() === '0' ? 'inner_miage_intal' : 'inter_miage_intal'
      return find(this.config(), ['extendName', extendName])?.values
    }
    return find(this.config(), ['extendName', 'inner_cardrate_dis'])?.values
  })

  getMonitorConfigListDefault() {
    this.apiService.fetchMonitorConfigListDefault(this.priceService.competitionName()).subscribe(res => {
      // console.log('default-list', res.data)
      if (res.data) {
        this.default_monitor_list.set(res.data)
        if (this.params() && this.params().isDefault === 'true') {
          const id = this.params().monitorId
          this.monitor_project.set(find(res.data, ['id', Number(id)]))
          this.monitor_type.set(find(res.data, ['id', Number(id)])?.monitorType)
        } else {
          this.monitor_project.set(res.data[0])
          this.monitor_type.set(res.data[0]?.monitorType)
        }
      }
    })
  }

  getMonitorConfigListTree() {
    this.apiService.fetchMonitorConfigListTree(this.priceService.competitionName()).subscribe(res => {
      // console.log('tree-list', res.data)
      if (res.data && isNotEmpty(res.data)) {
        this.treeData.set(res.data)
        if (this.params() && this.params().isDefault === 'false') {
          const id = this.params().monitorId
          const arr = flattenDeep(res.data.map(d => d.list))
          const _monitor = find(arr, ['id', Number(id)])
          this.monitor_project.set(_monitor)
          this.monitor_type.set(_monitor.monitorType)
        } else {
          this.monitor_project.set(res.data[0].list[0])
          this.monitor_type.set(res.data[0].list[0].monitorType)
        }
      } else {
        this.treeData.set([])
      }
      this.getTableData()
    })
  }

  private _subscribeChange() {
    combineLatest([
      this.formService.form.valueChanges.pipe(startWith(this.formService.form.value)),
      this.priceService.competitionFilter$,
    ])
      .pipe(startWith([]), debounceTime(100), takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.getTableData()
      })
  }

  private _subscribeToCompetitionNameChange() {
    this.priceService.competitionName$.pipe(debounceTime(100), takeUntilDestroyed(this.destroyRef)).subscribe(name => {
      this._fetchMonitorConfig()
    })
  }

  changeOrdType() {
    this.intal.set([null])
    this.getTableData()
  }

  clearFilter() {
    this.city.set([])
    this.price_type.set(null)
    this.c_ord_type.set('0')
    this.intal.set([null])
  }

  changeCurrentType() {
    this.monitor_project.set(null)
    this.clearFilter()
    this._fetchMonitorConfig()
  }

  private _fetchMonitorConfig() {
    if (this.current_type() === 'default') {
      this.getMonitorConfigListDefault()
    } else {
      this.getMonitorConfigListTree()
    }
  }

  renderNumberText(num, multiply100 = false) {
    if (num === null || num === undefined) {
      return {
        text: null,
        color: 'text-black',
      }
    }
    if (Number(num) < 0) {
      return {
        text: multiply100 ? (Number(num) * 100).toFixed(2) : Number(num).toFixed(2),
        color: 'text-green-500',
      }
    }
    if (Number(num) > 0) {
      return {
        text: multiply100 ? `+${(Number(num) * 100).toFixed(2)}` : `+${Number(num).toFixed(2)}`,
        color: 'text-red-500',
      }
    }
    return {
      text: Number(num).toFixed(2),
      color: 'text-black',
    }
  }

  @Before(isNotUndefined)
  changeProject(_data) {
    this.diffValuesMap.set(new Map())
    this.clearFilter()
    this.monitor_type.set(_data.monitorType)
    this.monitor_project.set(_data)
    this.getTableData()
  }

  ngOnInit(): void {
    this.apiService.fetchDimensionConfig('price_monitor_dimension').subscribe(res => {
      // console.log('config', res.data)
      if (res.status !== '00000') {
        return console.error(res.message || res.error)
      }
      const c_ord_type = res.data.price_save.find(item => item.keyName === 'c_ord_type')
      const price_type = res.data.price_save.find(item => item.keyName === 'price_type')
      const city_region = res.data.price_base.find(item => item.keyName === 'price_city_region')
      const city_name = res.data.price_base.find(item => item.keyName === 'price_city_name')
      this.config.set(res.data.price_save)
      this.orderTypeOptions.set(c_ord_type.values)
      this.priceTypeOptions.set(price_type.values)
      this.city_region.set(city_region.values)
      this.city_name.set(city_name.values)
    })
    this.apiService.fetchMetricsConfig('price_monitor_metrics_v3').subscribe(res => {
      if (res.data) {
        // console.log('metrics', res.data['price_monitor'])
        this.metrics.set(res.data['price_monitor']?.subMetric)
      } else {
        this.metrics.set([])
      }
    })
    this._subscribeChange()
    this._subscribeToCompetitionNameChange()
  }

  onItemChecked(_data) {
    this.checkedCity.set([_data])
    this.checkedIds.set(this.checkedCity().map(c => c.id))
    this.compare()
  }

  closeTag(_data) {
    const arr = this.checkedCity().filter(c => c.id !== _data.id)
    this.checkedCity.set(arr)
    this.checkedIds.set(this.checkedCity().map(c => c.id))
  }

  @SwitchMap()
  getTableData() {
    if (!this.monitor_project()) {
      return
    }
    this.checkedCity.set([])
    const body = this.formService.value()
    body.dimensions = [
      {
        id: null,
        extendName: 'dt',
        predefineCompareType: ['dt'],
      },
      {
        id: null,
        extendName: this.monitor_type() !== 2 ? 'city_region' : 'city_name',
      },
    ]
    if (this.monitor_type() !== 3) {
      body.dimensions.push(
        {
          id: null,
          extendName: 'c_ord_type',
        },
        {
          id: null,
          extendName: 'price_type',
        }
      )
      if (this.monitor_type() === 2) {
        body.dimensions.push({
          id: null,
          extendName: 'inner_cardrate_dis',
        })
      } else {
        body.dimensions.push({
          id: null,
          extendName: this.c_ord_type() === '0' ? 'inner_miage_intal' : 'inter_miage_intal',
        })
      }
    }
    body.dataFillConfig = {
      open: 0,
      fillRangeType: 1,
      fillValue: 1,
    }
    const items = []
    if (this.monitor_type() !== 3) {
      items.push({
        conditionType: 2,
        condition: '=',
        id: null,
        extendName: 'c_ord_type',
        value: [
          {
            key: this.c_ord_type(),
            value: this.c_ord_type() === '0' ? '市内' : '城际',
          },
        ],
        valueType: null,
      })
    }
    if (this.city().length !== 0) {
      items.push({
        conditionType: 2,
        condition: 'in',
        id: null,
        extendName: this.monitor_type() !== 2 ? 'city_region' : 'city_name',
        value: this.city(),
        valueType: null,
      })
    }
    if (this.price_type()) {
      items.push({
        conditionType: 2,
        condition: '=',
        id: null,
        extendName: 'price_type',
        value: [
          {
            key: this.price_type().key,
            value: this.price_type().value,
          },
        ],
        valueType: null,
      })
    }
    const _intal = this.intal().filter(i => i)
    if (_intal.length !== 0) {
      let extendName
      if (this.monitor_type() === 1) {
        extendName = this.c_ord_type() === '0' ? 'inner_miage_intal' : 'inter_miage_intal'
      } else {
        extendName = 'inner_cardrate_dis'
      }

      items.push({
        conditionType: 2,
        condition: 'in',
        id: null,
        extendName,
        value: _intal.map(i => {
          return {
            key: i.key,
            value: i.value,
          }
        }),
        valueType: null,
      })
    }
    body.filter = {
      items,
      type: null,
    }
    body.scene = 7
    body.metrics = this.monitor_project().metricsList
    const metricsFilter = []
    for (let [key, value] of this.diffValuesMap()) {
      metricsFilter.push(value)
    }
    // console.log('metricsFilter', metricsFilter)

    body.metricsFilter = {
      items: metricsFilter.filter(isNotNull),
    }
    // console.log('body', this.column())
    this.loading.set(true)
    this.apiService
      .postPriceMonitorData({
        ...body,
        monitorId: this.monitor_project().id,
      })
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        if (res.data) {
          res.data.data.forEach(d => {
            d.id = uniqueId()
          })
          // console.log('tabledata-', res.data)
          this.tbData.set(res.data.data)
          this.headers.set(res.data.headers)
        } else {
          this.tbData.set([])
        }
      })
  }

  compare() {
    let intalExtendName
    if (this.monitor_type() === 2) {
      intalExtendName = 'inner_cardrate_dis'
    } else {
      intalExtendName = this.c_ord_type() === '0' ? 'inner_miage_intal' : 'inter_miage_intal'
    }
    console.log('checkedcity', this.checkedCity())
    const cityExtendName = this.monitor_type() === 2 ? 'city_name' : 'city_region'
    const cityName = this.checkedCity().map(c => {
      return find(this.monitor_type() === 2 ? this.city_name() : this.city_region(), ['value', c[cityExtendName]])
    })
    const intal = this.checkedCity().map(c => {
      return find(this.intal_list(), ['value', c[intalExtendName]])
    })
    const orderType = this.orderTypeOptions().filter(o => o.key === this.c_ord_type())[0]
    const priceType: any = this.checkedCity().map(c => {
      return find(this.priceTypeOptions(), ['value', c.price_type])
    })
    if (this.monitor_type() === 2) {
      this.priceService.quickCompare({
        isReferencePrice: true,
        cityName: unionBy(cityName, 'value'),
        orderType,
        priceTypeList: unionBy(priceType, 'value'),
        miageIntalList: unionBy(intal, 'value'),
        scroll: true,
      })
    } else {
      this.priceService.quickCompare({
        isReferencePrice: false,
        cityRegions: unionBy(cityName, 'value'),
        orderType: this.monitor_type() === 3 ? null : orderType,
        priceTypeList: unionBy(priceType, 'value'),
        miageIntalList: unionBy(intal, 'value'),
        scroll: true,
      })
    }
    this.buriedPointService.addStat('dida_dpm_caerus_Price_Contrast_click', {
      page_name: this.page_name,
      Pricetab_name: '告警信息',
    })
  }
}
